import React, { ReactNode } from 'react'
import { StyleSheet, View } from 'react-native'

import { ChartLegend } from '@zeal/uikit/Chart/ChartLegend'
import { Color, colors } from '@zeal/uikit/colors'
import { Column } from '@zeal/uikit/Column'
import { Row } from '@zeal/uikit/Row'
import { Skeleton as UISkeleton } from '@zeal/uikit/Skeleton'
import { Text } from '@zeal/uikit/Text'

import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

const styles = StyleSheet.create({
    chartContainer: {
        flexDirection: 'row',
        width: '100%',
        columnGap: 2,
    },
})

type Segment<T> = {
    id: T
    label: string
    value: number
    displayValue: string
    color: Color
    icon?: (renderProps: { color: Color }) => ReactNode
}

type Props<T> = {
    title: ReactNode
    segments: Segment<T>[]
    onLegendClick?: (segment: Segment<T>) => void
}

export const StackedBarChart = function <T extends string>({
    segments,
    onLegendClick,
    title,
}: Props<T>) {
    const total = segments.reduce((acc, cur) => acc + cur.value, 0)
    return (
        <Column spacing={16}>
            <Text variant="title3" weight="semi_bold" color="gray5">
                {title}
            </Text>
            <Column spacing={12}>
                <View style={styles.chartContainer}>
                    {segments.map((segment) => (
                        <View
                            key={segment.id}
                            style={{
                                height: 32,
                                backgroundColor: colors[segment.color],
                                flexGrow: segment.value,
                            }}
                        />
                    ))}
                </View>
                <Column spacing={4}>
                    {segments.map((segment) => (
                        <Row spacing={0} alignX="stretch" key={segment.id}>
                            <ChartLegend
                                variant="small"
                                title={segment.label}
                                color={segment.color}
                                icon={segment.icon}
                                onClick={() => onLegendClick?.(segment)}
                            />
                            <Text
                                variant="paragraph"
                                weight="medium"
                                color="gray20"
                            >
                                {`${getFormattedPercentage(
                                    (segment.value / total) * 100
                                )} ${segment.displayValue}`}
                            </Text>
                        </Row>
                    ))}
                </Column>
            </Column>
        </Column>
    )
}

const Skeleton = ({ title }: { title: ReactNode }) => (
    <Column spacing={16}>
        <Text variant="title3" weight="semi_bold" color="gray5">
            {title}
        </Text>
        <Column spacing={12}>
            <UISkeleton variant="default" width="100%" height={32} />
            <Column spacing={4}>
                <UISkeleton variant="default" width="30%" height={16} />
                <UISkeleton variant="default" width="30%" height={16} />
                <UISkeleton variant="default" width="30%" height={16} />
            </Column>
        </Column>
    </Column>
)

StackedBarChart.Skeleton = Skeleton

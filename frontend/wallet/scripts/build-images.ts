import { readdir, writeFile } from 'fs/promises'
import imagemin from 'imagemin'
import imageminOptipng from 'imagemin-optipng'
import pMap from 'p-map'
import path from 'path'

import { keys } from '@zeal/toolkit/Object'

import { unsafeGetCurrencyInfo } from '@zeal/domains/Currency'
import { PredefinedNetwork } from '@zeal/domains/Network'

import currencies from '../../mobile/assets/data/currencies2.json'

const zapperNetworksMap: Record<PredefinedNetwork['name'], string> = {
    Ethereum: 'ethereum',
    Arbitrum: 'arbitrum',
    BSC: 'binance-smart-chain',
    Polygon: 'polygon',
    Fantom: 'fantom',
    Optimism: 'optimism',
    Gnosis: 'gnosis',
    Avalanche: 'avalanche',
    Celo: 'celo',
    Cronos: 'cronos',
    Mantle: 'mantle',
    Manta: 'manta',
    Aurora: 'aurora',
    Base: 'base',
    Blast: 'blast',
    Linea: 'linea',
    OPBNB: 'opbnb',
    PolygonZkevm: 'polygon-zevm',
    Sonic: 'soinic',
    zkSync: 'zksync',
}

const TARGET = path.join(__dirname, '../local_currency_icons')
async function fetchImage(imageUrl: string) {
    try {
        // eslint-disable-next-line no-restricted-globals
        const response = await fetch(imageUrl)
        if (!response.ok) {
            throw new Error(
                `Failed to fetch image: ${response.status} ${response.statusText}`
            )
        }

        return await response.arrayBuffer()
    } catch (error) {
        return null
    }
}

const main = async () => {
    // we dont want to overwrite icons, this gives a way to manually replace icons
    const currentIcons = await readdir(TARGET)
    const currentIconsSet = new Set(currentIcons)
    const currencyInfos = keys(currencies).map((id, index) => {
        const info = unsafeGetCurrencyInfo(id)
        return {
            address: info.address,
            index,
            zapperURL: `https://storage.googleapis.com/zapper-fi-assets/tokens/${zapperNetworksMap[info.network.name]}/${info.address}.png`,
            target: `${id}.png`,
        }
    })

    const mapper = async ({
        zapperURL,
        target,
        index,
    }: {
        index: number
        address: string
        zapperURL: string
        // network: string
        target: string
    }) => {
        if (index % 100 === 0) {
            // eslint-disable-next-line no-console
            console.log(index)
        }
        if (currentIconsSet.has(target)) {
            return
        }
        const imgData = await fetchImage(zapperURL)
        if (imgData) {
            const buff = Buffer.from(imgData)
            const optimizedBuffer = await imagemin.buffer(buff, {
                plugins: [imageminOptipng({ optimizationLevel: 5 })],
            })

            await writeFile(path.join(TARGET, target), optimizedBuffer)
        }
    }
    await pMap(currencyInfos, mapper, { concurrency: 100 })
}

main()

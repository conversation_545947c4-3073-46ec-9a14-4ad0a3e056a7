{"name": "wallet", "version": "0.1.11", "private": true, "scripts": {"build:dev": "ZEAL_ENV=development webpack --config webpack.config.js", "build:prod": "ZEAL_ENV=production webpack --config webpack.config.js", "compress:dev": "rm -Rf ./development_build.zip && cd ./development_build && zip -r ../development_build.zip ./*", "compress:prod": "rm -Rf ./production_build.zip && cd ./production_build && zip -r ../production_build.zip ./*", "inject-sourcemaps-debug-id": "yarn run-ts scripts/inject-sourcemaps-debug-id.ts", "lint": "yarn eslint --cache --cache-location ../../.eslintcache/wallet.cache --max-warnings 0 ./src", "release": "yarn upload-to-google && yarn upload-sourcemaps ../development_build && yarn upload-sourcemaps ../production_build", "run-ts": "ts-node --compiler-options '{\"types\": [\"node\"],\"module\": \"commonjs\", \"isolatedModules\": false}'", "start": "run-p start:webpack start:scss-types", "start:scss-types": "typed-scss-modules src --watch --nameFormat none --exportType default", "start:webpack": "webpack --config webpack.config.js", "test": "jest", "test:ci": "DEBUG_PRINT_LIMIT=100000000000 CI=true yarn test --silent --coverage --coverageReporters=text-summary --coverageReporters=json", "test:debug": "node --inspect-brk ./node_modules/.bin/jest --runInBand --no-cache", "update-known-networks": "yarn run-ts scripts/update-known-networks.ts", "build-coingecko-verified-currency-ids": "yarn tsx scripts/build-coingecko-verified-currency-ids.ts", "build-currency-matrix-json": "yarn tsx scripts/build-currency-matrix-json.ts", "dappradar": "yarn tsx scripts/build-dapp-radar-list.ts", "imgs": "aws s3 sync s3://zeal-cdn-prod/currencies ./local_currency_icons && yarn tsx scripts/build-images.ts && aws s3 sync ./local_currency_icons s3://zeal-cdn-prod/currencies", "upload-sourcemaps": "yarn run-ts scripts/upload-sourcemaps.ts", "upload-to-google": "yarn run-ts scripts/upload-to-google.ts", "convert:svg": "svgr --typescript --native --expand-props none --no-svgo --icon --out-dir ./public/svgs/flags2 ./public/svgs/flags"}, "lint-staged": {"*.{js,jsx,ts,tsx,mdx}": ["eslint --ignore-pattern '!.storybook' --max-warnings 0"]}, "dependencies": {"@dotlottie/react-player": "1.6.19", "@ethereumjs/tx": "4.0.0", "@ledgerhq/hw-app-eth": "6.35.7", "@ledgerhq/hw-transport-webhid": "6.28.5", "@lottiefiles/react-lottie-player": "3.5.3", "@metamask/eth-sig-util": "6.0.0", "@sentry/react": "7.107.0", "@sentry/tracing": "7.107.0", "@sumsub/websdk-react": "2.0.1", "@trezor/connect-web": "9.1.5", "@zeal/api": "workspace:*", "@zeal/domains": "workspace:*", "@zeal/toolkit": "workspace:*", "@zeal/uikit": "workspace:*", "axios": "0.27.2", "buffer": "6.0.3", "chrome-types": "0.1.254", "crc-32": "1.2.2", "eventemitter3": "5.0.1", "expo": "52.0.19", "expo-crypto": "14.0.2", "googleapis": "108.0.0", "p-map": "7.0.3", "react": "18.3.1", "react-dom": "18.3.1", "react-intl": "6.2.10", "react-native": "0.76.9", "react-native-safe-area-context": "4.12.0", "react-native-svg": "15.8.0", "react-native-web": "0.19.13", "stream-browserify": "3.0.0"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/plugin-transform-runtime": "7.22.15", "@babel/preset-env": "7.22.15", "@babel/preset-react": "7.22.15", "@babel/preset-typescript": "7.22.11", "@remix-run/router": "1.0.3", "@sentry/cli": "2.40.0", "@storybook/react": "7.4.0", "@svgr/cli": "8.1.0", "@testing-library/jest-dom": "5.16.4", "@testing-library/react": "13.3.0", "@testing-library/user-event": "13.5.0", "@types/circular-dependency-plugin": "^5", "@types/jest": "27.5.2", "@types/jest-image-snapshot": "5.1.0", "@types/node": "20.11.29", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@zeal/eslint-config": "workspace:*", "babel-jest": "27.4.2", "babel-plugin-named-exports-order": "0.0.2", "circular-dependency-plugin": "5.2.2", "copy-webpack-plugin": "11.0.0", "css-loader": "6.7.1", "esbuild-loader": "4.2.2", "eslint": "8.3.0", "html-webpack-plugin": "5.5.0", "image-minimizer-webpack-plugin": "3.8.3", "imagemin": "8.0.1", "imagemin-optipng": "8.0.0", "imagemin-svgo": "10.0.1", "jest": "27.4.3", "jest-canvas-mock": "2.4.0", "jest-environment-jsdom": "27.4.3", "jest-image-snapshot": "5.1.0", "jest-puppeteer": "6.1.0", "jest-resolve": "27.4.2", "jest-scss-transform": "1.0.3", "jest-transform-stub": "2.0.0", "jsdom": "22.1.0", "lint-staged": "15.2.2", "npm-run-all": "4.1.5", "prop-types": "15.8.1", "puppeteer": "15.1.1", "sass": "1.68.0", "sass-loader": "13.3.2", "style-loader": "3.3.3", "ts-loader": "9.3.1", "ts-node": "10.9.2", "tsconfig-paths-webpack-plugin": "3.5.2", "tsx": "4.19.1", "typed-scss-modules": "7.0.1", "typescript": "5.5.3", "webpack": "5.74.0", "webpack-assets-manifest": "5.1.0", "webpack-cli": "4.10.0", "webpack-merge": "5.9.0"}}
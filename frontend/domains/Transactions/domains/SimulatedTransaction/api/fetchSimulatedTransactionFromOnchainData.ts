import { notReachable } from '@zeal/toolkit'
import { abs } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { values } from '@zeal/toolkit/Object'
import {
    arrayOfLength,
    emptyArray,
    failure,
    nullable,
    oneOf,
    required,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { KnownCurrencies } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import {
    GNOSIS_EURE,
    MONERIUM_V1_TOKENS,
    MONERIUM_V2_TOKENS,
    PAYMASTER_ADDRESS,
} from '@zeal/domains/Currency/constants'
import { fetchRatesForDefaultCurrency } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { MoneyDTO } from '@zeal/domains/Money/helpers/parse'
import {
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import {
    ApprovalLog,
    ERC20TransferLog,
    NativeWrapperDepositLog,
    NativeWrapperWithdrawLog,
    ParsedLog,
    SafeModuleTransactionLog,
} from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { PAYMASTERS_FEE_RECEIVERS } from '@zeal/domains/UserOperation/constants'

import { getMethodName } from '../helpers/getMethodName'
import { SimulatedTransaction } from '../SimulatedTransaction'

const UNLIMITED_APPROVAL_THRESHOLD = 10n ** 27n

type SendERC20 = {
    type: 'send_erc20'
    to: Web3.address.Address
    amount: MoneyDTO
}

type SendNative = {
    type: 'send_native'
    to: Web3.address.Address
    amount: MoneyDTO
}

type Approval = {
    type: 'approval'
    amount: MoneyDTO
    spender: Web3.address.Address
}

type Unknown = {
    type: 'unknown'
    method: string | null
    receive: MoneyDTO[]
    send: MoneyDTO[]
}

type ClassifiedTransaction = SendERC20 | SendNative | Approval | Unknown

type ClassifiableLog =
    | ApprovalLog
    | ERC20TransferLog
    | SafeModuleTransactionLog
    | NativeWrapperDepositLog
    | NativeWrapperWithdrawLog

const filterParsedLogs = ({
    logs,
    networkHexId,
    sender,
}: {
    networkHexId: NetworkHexId
    sender: Web3.address.Address
    logs: ParsedLog[]
}): ClassifiableLog[] => {
    const classifiableLogs = logs.filter((log): log is ClassifiableLog => {
        switch (log.type) {
            case 'approval':
            case 'erc20_transfer':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
            case 'safe_module_transaction':
                return true

            case 'user_operation_event': // !!!!!
            case 'account_deployed':
            case 'added_owner':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'disable_module':
            case 'enable_module':
            case 'safe_received':
            case 'set_allowance':
            case 'threshold_updated':
            case 'unknown':
            case 'user_operation_revert_reason':
                return false
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    const hasV1MoneriumToken = classifiableLogs.some((log) => {
        switch (log.type) {
            case 'erc20_transfer':
                return MONERIUM_V1_TOKENS.has(log.currencyId)

            case 'approval':
            case 'safe_module_transaction':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            default:
                return notReachable(log)
        }
    })

    return classifiableLogs.filter((log) => {
        switch (log.type) {
            case 'erc20_transfer': {
                switch (true) {
                    // We can't just remove v1 or v2 logs from classification, because in some transactions we don't have both, we have either both or v2
                    case hasV1MoneriumToken &&
                        MONERIUM_V2_TOKENS.has(log.currencyId):
                        return false

                    case log.from === sender &&
                        log.to === PAYMASTERS_FEE_RECEIVERS[networkHexId]:
                        return false

                    default:
                        return true
                }
            }

            case 'approval':
                // We need to do paymaster approvals as part of gas abstraction, we don't want them to be visible in sims
                return log.spender !== PAYMASTER_ADDRESS

            case 'safe_module_transaction':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return true

            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
}

const parseSendNative = ({
    input,
    logs,
    network,
    sender,
    to,
    value,
}: {
    input: Hexadecimal.Hexadecimal | null
    logs: ClassifiableLog[]
    network: PredefinedNetwork | TestNetwork
    sender: Web3.address.Address
    to: Web3.address.Address
    value: Hexadecimal.Hexadecimal | null
}): Result<unknown, SendNative> =>
    oneOf({ logs, to, value, input, sender }, [
        shape({
            emptyInput: nullable(input),
            value: required(value).map(Hexadecimal.toBigInt),
        }),

        shape({
            emptyInput: nullable(input),
            value: arrayOfLength(1, logs)
                .map((logs) => logs[0])
                .andThen((log) => {
                    switch (log.type) {
                        case 'safe_module_transaction':
                            return success(log)
                        case 'approval':
                        case 'erc20_transfer':
                        case 'native_wrapper_deposit':
                        case 'native_wrapper_withdraw':
                            return failure({
                                type: 'not_safe_module_transaction',
                                log,
                            })

                        default:
                            return notReachable(log)
                    }
                })
                .map((log) => log.value),
        }),
    ]).map(({ value }) => ({
        type: 'send_native',
        amount: { amount: value, currencyId: network.nativeCurrency.id },
        to,
    }))

const parseSendERC20 = ({
    logs,
    sender,
}: {
    logs: ClassifiableLog[]
    sender: Web3.address.Address
}): Result<unknown, SendERC20> =>
    arrayOfLength(1, logs)
        .map((logs) => logs[0])
        .andThen((log) => {
            switch (log.type) {
                case 'erc20_transfer':
                    return success(log)
                case 'native_wrapper_withdraw':
                case 'native_wrapper_deposit':
                case 'safe_module_transaction':
                case 'approval':
                    return failure({ type: 'not_erc20_transfer_log', log })

                default:
                    return notReachable(log)
            }
        })
        .andThen((log) =>
            log.from === sender
                ? success(log)
                : failure({ type: 'wrong_erc20_transfer_sender', log })
        )
        .map((log) => ({
            type: 'send_erc20',
            amount: { amount: log.amount, currencyId: log.currencyId },
            to: log.to,
        }))

const parseApproval = ({
    sender,
    logs,
}: {
    logs: ClassifiableLog[]
    sender: Web3.address.Address
}): Result<unknown, Approval> => {
    const transferLogs = logs.filter((log) => {
        switch (log.type) {
            case 'erc20_transfer':
            case 'native_wrapper_withdraw':
            case 'native_wrapper_deposit':
                return true
            case 'safe_module_transaction':
            case 'approval':
                return false
            default:
                return notReachable(log)
        }
    })

    const approvalLogs = logs
        .filter((log): log is ApprovalLog => {
            switch (log.type) {
                case 'approval':
                    return true
                case 'erc20_transfer':
                case 'safe_module_transaction':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                default:
                    return notReachable(log)
            }
        })
        .filter((log) => log.currencyId !== GNOSIS_EURE.id)

    return shape({
        transferLogs: emptyArray(transferLogs),
        approvalLogs: arrayOfLength(1, approvalLogs),
    })
        .map((logs) => logs.approvalLogs[0])
        .andThen((log) =>
            log.owner === sender
                ? success(log)
                : failure({ type: 'wrong_approval_owner', log })
        )
        .map((log) => ({
            type: 'approval',
            amount: { amount: log.amount, currencyId: log.currencyId },
            spender: log.spender,
        }))
}

const parseUnknown = ({
    network,
    sender,
    input,
    nativeBalanceDiff,
    logs,
}: {
    network: PredefinedNetwork | TestNetwork
    logs: ClassifiableLog[]
    input: Hexadecimal.Hexadecimal | null
    nativeBalanceDiff: bigint
    sender: Web3.address.Address
}): Result<unknown, Unknown> => {
    const { erc20Receive, erc20Send } = logs.reduce(
        (acc, log) => {
            switch (log.type) {
                case 'erc20_transfer':
                    if (log.from === sender) {
                        acc.erc20Send.push({
                            amount: log.amount,
                            currencyId: log.currencyId,
                        })
                    }
                    if (log.to === sender) {
                        acc.erc20Receive.push({
                            amount: log.amount,
                            currencyId: log.currencyId,
                        })
                    }
                    return acc

                case 'native_wrapper_deposit':
                    if (log.to === sender) {
                        acc.erc20Receive.push({
                            amount: log.amount,
                            currencyId: log.currencyId,
                        })
                    }
                    return acc

                case 'native_wrapper_withdraw':
                    if (log.from === sender) {
                        acc.erc20Send.push({
                            amount: log.amount,
                            currencyId: log.currencyId,
                        })
                    }
                    return acc

                case 'safe_module_transaction':
                case 'approval':
                    return acc
                default:
                    return notReachable(log)
            }
        },
        {
            erc20Receive: [],
            erc20Send: [],
        } as {
            erc20Receive: MoneyDTO[]
            erc20Send: MoneyDTO[]
        }
    )

    const nativeBalanceDiffSend: MoneyDTO[] =
        nativeBalanceDiff < 0n
            ? [
                  {
                      amount: abs(nativeBalanceDiff),
                      currencyId: network.nativeCurrency.id,
                  },
              ]
            : []

    const nativeBalanceDiffReceive: MoneyDTO[] =
        nativeBalanceDiff > 0n
            ? [
                  {
                      amount: abs(nativeBalanceDiff),
                      currencyId: network.nativeCurrency.id,
                  },
              ]
            : []

    return success({
        type: 'unknown',
        method: input ? getMethodName({ input: input }) : null,
        send: [...erc20Send, ...nativeBalanceDiffSend],
        receive: [...erc20Receive, ...nativeBalanceDiffReceive],
    })
}

const parseClassifiedTransaction = ({
    network,
    logs,
    to,
    value,
    input,
    nativeBalanceDiff,
    sender,
}: {
    logs: ParsedLog[]
    to: Web3.address.Address
    value: Hexadecimal.Hexadecimal | null
    input: Hexadecimal.Hexadecimal | null
    sender: Web3.address.Address
    nativeBalanceDiff: bigint
    network: PredefinedNetwork | TestNetwork
}): Result<unknown, ClassifiedTransaction> => {
    const logsForClassification = filterParsedLogs({
        logs,
        networkHexId: network.hexChainId,
        sender,
    })

    return oneOf({ network, logs, to, value, input, sender }, [
        parseSendNative({
            input,
            logs: logsForClassification,
            network,
            sender,
            to,
            value,
        }),
        parseSendERC20({ logs: logsForClassification, sender }),
        parseApproval({ logs: logsForClassification, sender }),
        parseUnknown({
            input,
            logs: logsForClassification,
            network,
            sender,
            nativeBalanceDiff,
        }),
    ])
}

const enrichClassifiedTransaction = async ({
    classifiedTransaction,
    network,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    classifiedTransaction: ClassifiedTransaction
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    switch (classifiedTransaction.type) {
        case 'send_native':
        case 'send_erc20': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [classifiedTransaction.amount.currencyId],
                networkRPCMap,
            })

            // TODO @resetko-zeal find a way to fetch both cryptocurrencies and rates at the same time in parallel
            const rates = await fetchRatesForDefaultCurrency({
                cryptoCurrencies: values(knownCryptoCurrencies),
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            const cryptoCurrency =
                knownCryptoCurrencies[
                    classifiedTransaction.amount.currencyId
                ] || null

            if (!cryptoCurrency) {
                throw new ImperativeError('Failed to fetch cryptocurrency for ')
            }

            const rate = rates[classifiedTransaction.amount.currencyId]

            return {
                simulatedTransaction: {
                    type: 'P2PTransaction',
                    toAddress: classifiedTransaction.to,
                    token: {
                        amount: classifiedTransaction.amount,
                        direction: 'Send',
                        priceInDefaultCurrency: rate
                            ? applyRate2({
                                  baseAmount: {
                                      amount: classifiedTransaction.amount
                                          .amount,
                                      currency: cryptoCurrency,
                                  },
                                  rate,
                              })
                            : null,
                    },
                },
                knownCurrencies: {
                    [defaultCurrencyConfig.defaultCurrency.id]:
                        defaultCurrencyConfig.defaultCurrency,
                    [cryptoCurrency.id]: cryptoCurrency,
                },
            }
        }

        case 'approval': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [classifiedTransaction.amount.currencyId],
                networkRPCMap,
            })

            const cryptoCurrency =
                knownCryptoCurrencies[
                    classifiedTransaction.amount.currencyId
                ] || null

            if (!cryptoCurrency) {
                throw new ImperativeError('Failed to fetch cryptocurrency for ')
            }

            const cryptoMoney: CryptoMoney = {
                amount: classifiedTransaction.amount.amount,
                currency: cryptoCurrency,
            }

            return {
                simulatedTransaction: {
                    type: 'ApprovalTransaction',
                    // FIXME @resetko-zeal smart contract lookup
                    approveTo: {
                        address: classifiedTransaction.spender,
                        logo: null,
                        name: null,
                        website: null,
                        networkHexId: network.hexChainId,
                    },
                    amount:
                        classifiedTransaction.amount.amount >
                        UNLIMITED_APPROVAL_THRESHOLD
                            ? { type: 'Unlimited', amount: cryptoMoney } // TODO @resetko-zeal feels that this is presentation layer info
                            : { type: 'Limited', amount: cryptoMoney },
                },
                knownCurrencies: knownCryptoCurrencies,
            }
        }

        case 'unknown': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [
                    ...classifiedTransaction.receive.map(
                        (amount) => amount.currencyId
                    ),
                    ...classifiedTransaction.send.map(
                        (amount) => amount.currencyId
                    ),
                ],
                networkRPCMap,
            })

            // TODO @resetko-zeal find a way to fetch both cryptocurrencies and rates at the same time in parallel
            const rates = await fetchRatesForDefaultCurrency({
                cryptoCurrencies: values(knownCryptoCurrencies),
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            return {
                simulatedTransaction: {
                    type: 'UnknownTransaction',
                    method: classifiedTransaction.method || '',
                    nfts: [], // TODO @resetko-zeal classify NFT logs
                    tokens: [
                        ...classifiedTransaction.send.map((sendDto) => {
                            const currency =
                                knownCryptoCurrencies[sendDto.currencyId] ||
                                null

                            if (!currency) {
                                throw new ImperativeError(
                                    'Failed to find CryptoCurrency for send token'
                                )
                            }

                            const rate = rates[currency.id] || null

                            return {
                                direction: 'Send' as const,
                                amount: sendDto,
                                priceInDefaultCurrency: rate
                                    ? applyRate2({
                                          baseAmount: {
                                              amount: sendDto.amount,
                                              currency,
                                          },
                                          rate,
                                      })
                                    : null,
                            }
                        }),
                        ...classifiedTransaction.receive.map((receiveDto) => {
                            const currency =
                                knownCryptoCurrencies[receiveDto.currencyId] ||
                                null

                            if (!currency) {
                                throw new ImperativeError(
                                    'Failed to find CryptoCurrency for receive token'
                                )
                            }

                            const rate = rates[currency.id] || null

                            return {
                                direction: 'Receive' as const,
                                amount: receiveDto,
                                priceInDefaultCurrency: rate
                                    ? applyRate2({
                                          baseAmount: {
                                              amount: receiveDto.amount,
                                              currency,
                                          },
                                          rate,
                                      })
                                    : null,
                            }
                        }),
                    ],
                },
                knownCurrencies: {
                    ...knownCryptoCurrencies,
                    [defaultCurrencyConfig.defaultCurrency.id]:
                        defaultCurrencyConfig.defaultCurrency,
                },
            }
        }

        default:
            return notReachable(classifiedTransaction)
    }
}

export const fetchSimulatedTransactionFromOnchainData = async ({
    network,
    logs,
    to,
    value,
    input,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    nativeBalanceDiff,
    sender,
    signal,
}: {
    logs: ParsedLog[]
    to: Web3.address.Address
    value: Hexadecimal.Hexadecimal | null
    input: Hexadecimal.Hexadecimal | null
    sender: Web3.address.Address
    network: PredefinedNetwork | TestNetwork // TODO @resetko-zeal do we need a type for (logs to value input sender network)
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    nativeBalanceDiff: bigint
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    const classifiedTransaction = parseClassifiedTransaction({
        network,
        sender,
        logs,
        to,
        value,
        nativeBalanceDiff,
        input,
    }).getSuccessResultOrThrow(
        'Failed to classify transaction for user op main transaction'
    )

    return enrichClassifiedTransaction({
        classifiedTransaction,
        defaultCurrencyConfig,
        network,
        networkMap,
        networkRPCMap,
        signal,
    })
}

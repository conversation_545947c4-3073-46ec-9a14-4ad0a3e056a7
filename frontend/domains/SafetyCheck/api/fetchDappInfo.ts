import { fetchStaticData } from '@zeal/api/fetchStaticData'

import { success } from '@zeal/toolkit/Result'

export type DappRadarInfo = {
    name: string
    logo: null | string
    website: string
}

const WHITE_LIST: Record<string, DappRadarInfo | null> = {
    'my.gnosispay.com': {
        name: 'GnosisPay',
        logo: 'https://my.gnosispay.com/favicon-96x96.png',
        website: 'https://my.gnosispay.com',
    },
}

export const fetchDappInfo = async ({
    hostname,
}: {
    hostname: string
}): Promise<DappRadarInfo | null> => {
    const whiteListed = WHITE_LIST[hostname]
    if (whiteListed) {
        return whiteListed
    }
    const dappradaraData = await fetchStaticData(
        '@zeal/assets/data/dappradar.json',
        (input) => {
            // !!! do we really need to parse couple of MB each time app starts? !!!
            return success(input as Record<string, DappRadarInfo | null>)
        }
    )
    return dappradaraData[hostname]
}

import { DAppSiteInfo } from '@zeal/domains/DApp'

import { fetchBlockaidScanHostname } from './fetchBlockaidScanHostname'
import { fetchDappInfo } from './fetchDappInfo'
import { fetchDappVerificationSafetyCheck } from './fetchDappVerificationSafetyCheck'

import {
    ConnectionSafetyCheck,
    SuspiciousCharactersCheckFailed,
    SuspiciousCharactersCheckPassed,
} from '../SafetyCheck'

export type ConnectionSafetyChecksResponse = {
    dAppInfo: DAppSiteInfo
    checks: ConnectionSafetyCheck[]
}

export const fetchConnectionSafetychecks = async (
    dappInfo: DAppSiteInfo
): Promise<ConnectionSafetyChecksResponse> => {
    const staticDappInfo = await fetchDappInfo({ hostname: dappInfo.hostname })
    const checks = await Promise.all([
        checkSuspiciousCharactersCheck({ hostname: dappInfo.hostname }),
        fetchBlockaidScanHostname({ hostname: dappInfo.hostname }),
        fetchDappVerificationSafetyCheck({ hostname: dappInfo.hostname }),
    ])
    return {
        dAppInfo: staticDappInfo
            ? {
                  avatar: staticDappInfo.logo,
                  hostname: dappInfo.hostname,
                  title: staticDappInfo.name,
              }
            : dappInfo,
        checks,
    }
}

const NON_ALPHANUMERIC_HOST_PATTERN = new RegExp('[^a-z\\d\\.\\-]+')

const checkSuspiciousCharactersCheck = ({
    hostname,
}: {
    hostname: string
}): SuspiciousCharactersCheckFailed | SuspiciousCharactersCheckPassed => {
    const match = NON_ALPHANUMERIC_HOST_PATTERN.exec(hostname)
    if (match) {
        const fullMatch = match[0]
        const start = match.index
        const end = start + fullMatch.length
        return {
            type: 'SuspiciousCharactersCheck',
            severity: 'Danger',
            state: 'Failed',
            suspiciousPart: {
                end,
                start,
            },
        }
    }

    return {
        type: 'SuspiciousCharactersCheck',
        severity: 'Danger',
        state: 'Passed',
    }
}

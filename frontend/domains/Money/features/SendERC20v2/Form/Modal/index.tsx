import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { BoldHeart } from '@zeal/uikit/Icon/BoldHeart'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Modal as UIModal } from '@zeal/uikit/Modal'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SelectToAddress } from '@zeal/domains/Account/features/SelectToAddress'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { NonSponsoredUserOperationFee } from '@zeal/domains/UserOperation'
import { GasTokenSelector } from '@zeal/domains/UserOperation/components/GasTokenSelector'

import { SelectFromToken } from './SelectFromToken'

import { Pollable } from '../../fetch'

export type Props = {
    account: Account
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    state: State
    pollable: Pollable
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'from_token_selector'; portfolio: ServerPortfolio2 }
    | { type: 'to_wallet_selector' }
    | { type: 'edit_eoa_fees' }
    | {
          type: 'select_gas_abstraction_token'
          portfolio: ServerPortfolio2
          fee: NonSponsoredUserOperationFee
      }
    | { type: 'sponsored_fee_info'; network: Network }

type Msg =
    | MsgOf<typeof SelectFromToken>
    | MsgOf<typeof SelectToAddress>
    | MsgOf<typeof GasTokenSelector>

export const Modal = ({
    account,
    cardConfig,
    portfolioMap,
    keyStoreMap,
    accountsMap,
    networkRPCMap,
    sessionPassword,
    customCurrencies,
    installationId,
    defaultCurrencyConfig,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    gasCurrencyPresetMap,
    state,
    pollable,
    isEthereumNetworkFeeWarningSeen,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'sponsored_fee_info':
            return (
                <Popup.Layout onMsg={onMsg}>
                    <ActionBar
                        right={
                            <IconButton
                                variant="on_light"
                                onClick={() => {
                                    onMsg({ type: 'close' })
                                }}
                            >
                                {({ color }) => (
                                    <CloseCross size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <Header
                        title={
                            <FormattedMessage
                                id="active_and_tracked_wallets.title"
                                defaultMessage="Zeal covers all your fees on {network}, allowing you to transact for free!"
                                values={{ network: state.network.name }}
                            />
                        }
                        icon={({ size }) => (
                            <BoldHeart size={size} color="teal40" />
                        )}
                    />
                    <Popup.Actions>
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            <FormattedMessage
                                id="action.close"
                                defaultMessage="Close"
                            />
                        </Button>
                    </Popup.Actions>
                </Popup.Layout>
            )
        case 'from_token_selector':
            return (
                <UIModal>
                    <SelectFromToken
                        onMsg={onMsg}
                        portfolio={state.portfolio}
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        selectedCurrency={pollable.params.form.cryptoCurrency}
                    />
                </UIModal>
            )
        case 'to_wallet_selector':
            return (
                <UIModal>
                    <SelectToAddress
                        account={account}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencies={customCurrencies}
                        sessionPassword={sessionPassword}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        toAddress={pollable.params.form.toAddress}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        cardConfig={cardConfig}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'edit_eoa_fees':
            return null // TODO: @max part two
        case 'select_gas_abstraction_token':
            return (
                <UIModal>
                    <GasTokenSelector
                        installationId={installationId}
                        fee={state.fee}
                        networkMap={networkMap}
                        portfolio={state.portfolio}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}

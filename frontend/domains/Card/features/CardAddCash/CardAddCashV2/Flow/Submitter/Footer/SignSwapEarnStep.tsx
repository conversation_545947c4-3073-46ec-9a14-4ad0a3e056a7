import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { SubmittedEarnSignTypedDataCardTopUp } from '@zeal/domains/Card'
import { submitBungeeIntentQuote } from '@zeal/domains/Currency/domains/Exchange/domains/Bungee/api/submitBungeeIntentQuote'
import { prepareAndSubmitSwap } from '@zeal/domains/Currency/domains/SwapsIO/api/prepareAndSubmitSwap'
import {
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP,
} from '@zeal/domains/Earn/constants'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { AppErrorPopupWithHardwareWalletErrors } from './AppErrorPopupWithHardwareWalletErrors'
import { LoadingLayout } from './LoadingLayout'

import { CardTopUpEOAQuote, CardTopUpSafeQuote } from '../../../types'

type Props = {
    quote:
        | Extract<
              CardTopUpEOAQuote,
              {
                  type:
                      | 'earn_eoa_sign_typed_data'
                      | 'earn_eoa_sign_typed_data_with_approval'
              }
          >
        | Extract<
              CardTopUpSafeQuote,
              {
                  type: 'earn_safe_sign_typed_data_with_optional_approval'
              }
          >

    fromAmount: CryptoMoney
    senderAddress: Web3.address.Address
    networkMap: NetworkMap
    cardSafeAddress: Web3.address.Address
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp: SubmittedEarnSignTypedDataCardTopUp
      }

const fetch = async ({
    quote,
    senderAddress,
    fromAmount,
    networkMap,
    cardSafeAddress,
    sessionPassword,
    signal,
}: {
    senderAddress: Web3.address.Address
    quote:
        | Extract<
              CardTopUpEOAQuote,
              {
                  type:
                      | 'earn_eoa_sign_typed_data'
                      | 'earn_eoa_sign_typed_data_with_approval'
              }
          >
        | Extract<
              CardTopUpSafeQuote,
              {
                  type: 'earn_safe_sign_typed_data_with_optional_approval'
              }
          >
    cardSafeAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    fromAmount: CryptoMoney
    signal?: AbortSignal
}): Promise<SubmittedEarnSignTypedDataCardTopUp> => {
    const network = EARN_NETWORK
    const takerType =
        EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP[fromAmount.currency.id]

    if (!takerType) {
        throw new ImperativeError('Taker not found for currency', {
            currencyId: fromAmount.currency.id,
        })
    }

    switch (quote.provider.type) {
        case 'bungee': {
            const { quoteId, quoteRequestType, signTypedData, witness } =
                quote.provider.quoteInfo

            const signedMessage = await signMessage({
                sessionPassword,
                request: signTypedData,
                keyStore: quote.keystore,
                network,
                dApp: null,
            })

            const requestHash = await submitBungeeIntentQuote({
                quoteId,
                quoteWiness: witness,
                userSignature: signedMessage,
                quoteRequestType,
            })

            return {
                type: 'earn_sign_typed_data',
                provider: 'bungee',
                takerType,
                toAmount: quote.toAmount,
                keyStore: quote.keystore,
                startedAtMs: Date.now(),
                state: { type: 'waiting_for_swap' },
                quoteRequestHash: requestHash,
                cardSafeAddress,
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardEarnBungee',
                },
            }
        }
        case 'swaps_io':
            const swapsIOSwapRequest = await prepareAndSubmitSwap({
                sender: senderAddress,
                receiver: cardSafeAddress,
                keyStore: quote.keystore,
                from: fromAmount,
                to: quote.toAmount,
                networkMap,
                sessionPassword,
                signal,
            })

            return {
                type: 'earn_sign_typed_data',
                provider: 'swaps_io',
                takerType,
                toAmount: quote.toAmount,
                keyStore: quote.keystore,
                startedAtMs: Date.now(),
                state: { type: 'waiting_for_swap' },
                quoteRequestHash: swapsIOSwapRequest.hash,
                cardSafeAddress,
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardSwapsIO',
                },
            }
        default:
            return notReachable(quote.provider)
    }
}

export const SignSwapEarnStep = ({
    fromAmount,
    sessionPassword,
    quote,
    networkMap,
    installationId,
    cardSafeAddress,
    senderAddress,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            fromAmount,
            networkMap,
            cardSafeAddress,
            quote,
            senderAddress,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_card_top_up_success',
                    submittedCardTopUp: loadable.data,
                })
                return
            case 'loading':
            case 'error':
                return
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveOnMsg])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return <LoadingLayout variant="step_2_of_2" />
        case 'error':
            return (
                <>
                    <LoadingLayout variant="step_2_of_2" />
                    <AppErrorPopupWithHardwareWalletErrors
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_trezor_error_close':
                                case 'on_ledger_error_close':
                                case 'close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'on_sync_ledger_click':
                                case 'on_sync_trezor_click':
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            fromAmount,
                                            sessionPassword,
                                            cardSafeAddress,
                                            networkMap,
                                            senderAddress,
                                            quote,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'

import { EarnEOAEUReWithdrawalFooter } from './EarnEOAEUReWithdrawalFooter'
import { EarnEOASignFooter } from './EarnEOASignFooter'
import { EarnEOASignWithApprovalFooter } from './EarnEOASignWithApprovalFooter'
import { EarnSafeSignWithApprovalFooter } from './EarnSafeSignWithApprovalFooter'
import { SingleStepSignTypedDataFooter } from './SingleStepSignTypedDataFooter'
import { SingleStepTxEOAFooter } from './SingleStepTxEOAFooter'
import { SingleStepTxSafeFooter } from './SingleStepTxSafeFooter'
import { SwapEOASignWithApprovalFooter } from './SwapEOASignWithApprovalFooter'
import { SwapSafeSignWithApprovalOrDeploymentFooter } from './SwapSafeSignWithApprovalOrDeploymentFooter'

import { CardTopUpRequest } from '../../../types'

type Props = {
    cardTopUpRequest: CardTopUpRequest

    installationId: string
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SingleStepTxEOAFooter>
    | MsgOf<typeof SingleStepSignTypedDataFooter>
    | MsgOf<typeof SingleStepTxSafeFooter>
    | MsgOf<typeof SwapEOASignWithApprovalFooter>
    | MsgOf<typeof SwapSafeSignWithApprovalOrDeploymentFooter>
    | MsgOf<typeof EarnSafeSignWithApprovalFooter>
    | MsgOf<typeof EarnEOASignFooter>
    | MsgOf<typeof EarnEOAEUReWithdrawalFooter>

export const Footer = ({
    onMsg,
    cardTopUpRequest,
    sessionPassword,
    networkRPCMap,
    installationId,
    networkMap,
}: Props) => {
    switch (cardTopUpRequest.quote.type) {
        case 'send_eoa':
        case 'swap_eoa_native_send_transaction':
            return (
                <SingleStepTxEOAFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    fromAmount={cardTopUpRequest.fromAmount}
                    fromAddress={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    quote={cardTopUpRequest.quote}
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    onMsg={onMsg}
                />
            )
        case 'swap_eoa_sign_typed_data':
        case 'swap_safe_sign_typed_data':
            return (
                <SingleStepSignTypedDataFooter
                    quote={cardTopUpRequest.quote}
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    networkMap={networkMap}
                    fromAmount={cardTopUpRequest.fromAmount}
                    sender={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'send_safe':
        case 'swap_safe_native_send_transaction':
        case 'earn_safe_eure_withdrawal':
            return (
                <SingleStepTxSafeFooter
                    sender={cardTopUpRequest.senderAddress}
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    fromAmount={cardTopUpRequest.fromAmount}
                    sessionPassword={sessionPassword}
                    quote={cardTopUpRequest.quote}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        case 'swap_eoa_sign_typed_data_with_approval':
            return (
                <SwapEOASignWithApprovalFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAmount={cardTopUpRequest.fromAmount}
                    fromAddress={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    networkMap={networkMap}
                    onMsg={onMsg}
                />
            )
        case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
            return (
                <SwapSafeSignWithApprovalOrDeploymentFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAmount={cardTopUpRequest.fromAmount}
                    fromAddress={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'earn_eoa_sign_typed_data_with_approval':
            return (
                <EarnEOASignWithApprovalFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAddress={cardTopUpRequest.senderAddress}
                    fromAmount={cardTopUpRequest.fromAmount}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'earn_eoa_sign_typed_data':
            return (
                <EarnEOASignFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAddress={cardTopUpRequest.senderAddress}
                    fromAmount={cardTopUpRequest.fromAmount}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'earn_safe_sign_typed_data_with_optional_approval':
            return (
                <EarnSafeSignWithApprovalFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAmount={cardTopUpRequest.fromAmount}
                    senderAddress={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        case 'earn_eoa_eure_withdrawal':
            return (
                <EarnEOAEUReWithdrawalFooter
                    cardSafeAddress={cardTopUpRequest.cardSafeAddress}
                    quote={cardTopUpRequest.quote}
                    fromAmount={cardTopUpRequest.fromAmount}
                    fromAddress={cardTopUpRequest.senderAddress}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(cardTopUpRequest.quote)
    }
}

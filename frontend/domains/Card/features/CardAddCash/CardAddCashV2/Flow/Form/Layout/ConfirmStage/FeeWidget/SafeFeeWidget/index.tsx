import React, { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { FeeDetailsPopup } from './FeeDetailsPopup'

import {
    getSafeQuoteFeeBreakdown,
    SafeQuoteFeeBreakdown,
} from '../../../../../../helpers/getQuoteFeeBreakdown'
import { CardTopUpSafeQuote } from '../../../../../../types'

type Props = {
    fromAmountInDefaultCurrency: FiatMoney | null
    topUpRequestSafeQuote: CardTopUpSafeQuote

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof FeeDetailsPopup>,
    {
        type:
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
    }
>

export const SafeFeeWidget = ({
    fromAmountInDefaultCurrency,
    topUpRequestSafeQuote,
    gasCurrencyPresetMap,
    networkMap,
    defaultCurrencyConfig,
    portfolio,
    installationId,
    onMsg,
}: Props) => {
    const feeBreakdown = getSafeQuoteFeeBreakdown({
        fromAmountInDefaultCurrency,
        quote: topUpRequestSafeQuote,
    })

    const [modal, setModal] = useState<State>({ type: 'closed' })

    return (
        <>
            <Layout
                feeBreakdown={feeBreakdown}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_fee_widget_clicked':
                            setModal({ type: 'fee_details_modal' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg.type)
                    }
                }}
            />

            <Modal
                installationId={installationId}
                feeBreakdown={feeBreakdown}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                networkMap={networkMap}
                portfolio={portfolio}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break

                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}

type State = { type: 'closed' } | { type: 'fee_details_modal' }

const Layout = ({
    feeBreakdown,
    onMsg,
}: {
    feeBreakdown: SafeQuoteFeeBreakdown
    onMsg: (msg: { type: 'on_fee_widget_clicked' }) => void
}) => (
    <FeeInputButton
        disabled={false}
        onClick={() =>
            onMsg({
                type: 'on_fee_widget_clicked',
            })
        }
        left={
            <Row spacing={0}>
                <Text variant="paragraph" weight="regular" color="textPrimary">
                    <FormattedMessage
                        id="network-fee-widget.title"
                        defaultMessage="Fees"
                    />
                </Text>
            </Row>
        }
        right={
            <Row spacing={4}>
                {(() => {
                    switch (feeBreakdown.type) {
                        case 'provider_fee_only':
                            return null
                        case 'provider_and_network_fee':
                            return (
                                <Text
                                    variant="paragraph"
                                    color="gray40"
                                    weight="regular"
                                >
                                    {
                                        feeBreakdown.selectedFee.selectedFee
                                            .feeInTokenCurrency.currency.code
                                    }
                                </Text>
                            )
                        default:
                            return notReachable(feeBreakdown)
                    }
                })()}
                <Text variant="paragraph" color="textPrimary" weight="regular">
                    {feeBreakdown.totalFee ? (
                        <FormattedMoneyCompact money={feeBreakdown.totalFee} />
                    ) : (
                        <FormattedMessage
                            id="fee.unknown"
                            defaultMessage="Unknown"
                        />
                    )}
                </Text>
                <InfoCircleOutline size={20} color="gray40" />
            </Row>
        }
    />
)

type ModalMsg = { type: 'close' } | MsgOf<typeof FeeDetailsPopup>

const Modal = ({
    feeBreakdown,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    networkMap,
    onMsg,
    portfolio,
    state,
    installationId,
}: {
    feeBreakdown: SafeQuoteFeeBreakdown

    gasCurrencyPresetMap: GasCurrencyPresetMap
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    state: State
    onMsg: (msg: ModalMsg) => void
}) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'fee_details_modal':
            return (
                <FeeDetailsPopup
                    installationId={installationId}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feeBreakdown={feeBreakdown}
                    networkMap={networkMap}
                    onMsg={onMsg}
                    portfolio={portfolio}
                />
            )

        default:
            return notReachable(state)
    }
}

import { FormattedMessage } from 'react-intl'

import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { Row } from '@zeal/uikit/Row'
import { Spinner } from '@zeal/uikit/Spinner'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { EOAFeeWidget } from './EOAFeeWidget'
import { SafeFeeWidget } from './SafeFeeWidget'

import { QuotePollable } from '../../../../../types'

type Props = {
    quotePollable: QuotePollable
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SafeFeeWidget>

export const FeeWidget = ({ quotePollable, installationId, onMsg }: Props) => {
    switch (quotePollable.type) {
        case 'loaded':
        case 'subsequent_failed': {
            const { fromAmountInDefaultCurrency } = quotePollable.data
            const { defaultCurrencyConfig, networkMap, gasCurrencyPresetMap } =
                quotePollable.params

            const quote = quotePollable.data.quote.getSuccessResult() || null

            if (!quote) {
                return null
            }

            switch (quote.type) {
                case 'earn_track_only':
                case 'send_track_only':
                case 'swap_track_only':
                    return null

                case 'send_eoa':
                case 'swap_eoa_sign_typed_data':
                case 'swap_eoa_sign_typed_data_with_approval':
                case 'swap_eoa_native_send_transaction':
                case 'earn_eoa_sign_typed_data_with_approval':
                case 'earn_eoa_sign_typed_data':
                case 'earn_eoa_eure_withdrawal':
                    return (
                        <EOAFeeWidget
                            fromAmountInDefaultCurrency={
                                fromAmountInDefaultCurrency
                            }
                            topUpRequestEOAQuote={quote}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                        />
                    )

                case 'send_safe':
                case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
                case 'swap_safe_sign_typed_data':
                case 'swap_safe_native_send_transaction':
                case 'earn_safe_sign_typed_data_with_optional_approval':
                case 'earn_safe_eure_withdrawal':
                    return (
                        <SafeFeeWidget
                            installationId={installationId}
                            fromAmountInDefaultCurrency={
                                fromAmountInDefaultCurrency
                            }
                            topUpRequestSafeQuote={quote}
                            onMsg={onMsg}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            networkMap={networkMap}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            portfolio={quotePollable.params.senderPortfolio}
                        />
                    )

                default:
                    return notReachable(quote)
            }
        }

        case 'loading':
        case 'reloading':
            return (
                <FeeInputButton
                    disabled
                    left={
                        <Row spacing={4}>
                            <Text
                                variant="paragraph"
                                weight="regular"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="network-fee-widget.title"
                                    defaultMessage="Fees"
                                />
                            </Text>
                        </Row>
                    }
                    right={
                        <Row spacing={4}>
                            <Spinner
                                variant="regular"
                                size={18}
                                color="teal40"
                            />
                        </Row>
                    }
                />
            )

        case 'error':
            return null

        default:
            return notReachable(quotePollable)
    }
}

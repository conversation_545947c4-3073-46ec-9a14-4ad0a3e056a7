import React, { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

import { SafeQuoteFeeBreakdown } from '../../../../../../../helpers/getQuoteFeeBreakdown'

type Props = {
    feeBreakdown: SafeQuoteFeeBreakdown

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

export const FeeDetailsPopup = ({
    feeBreakdown,
    gasCurrencyPresetMap,
    networkMap,
    defaultCurrencyConfig,
    portfolio,
    installationId,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                feeBreakdown={feeBreakdown}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break

                        case 'on_edit_network_fee_clicked':
                            setModal({
                                type: 'select_gas_abstraction_fee_currency',
                                fee: msg.fee,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                installationId={installationId}
                state={modal}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                networkMap={networkMap}
                portfolio={portfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break

                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}

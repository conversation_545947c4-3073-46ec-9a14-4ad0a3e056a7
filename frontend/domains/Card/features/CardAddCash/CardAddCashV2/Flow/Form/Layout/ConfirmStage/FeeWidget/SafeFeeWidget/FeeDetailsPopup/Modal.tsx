import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { NonSponsoredUserOperationFee } from '@zeal/domains/UserOperation'
import { GasTokenSelector } from '@zeal/domains/UserOperation/components/GasTokenSelector'

type Props = {
    state: State
    gasCurrencyPresetMap: GasCurrencyPresetMap
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    installationId: string
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | {
          type: 'select_gas_abstraction_fee_currency'
          fee: NonSponsoredUserOperationFee
      }

type Msg = { type: 'close' } | MsgOf<typeof GasTokenSelector>

export const Modal = ({
    state,
    gasCurrencyPresetMap,
    onMsg,
    portfolio,
    networkMap,
    installationId,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_gas_abstraction_fee_currency':
            return (
                <UIModal>
                    <GasTokenSelector
                        installationId={installationId}
                        fee={state.fee}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        networkMap={networkMap}
                        portfolio={portfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}

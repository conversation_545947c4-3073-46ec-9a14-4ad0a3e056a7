import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { SubmittedCardTopUp } from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { fetchBungeeIntentQuoteStatus } from '@zeal/domains/Currency/domains/Exchange/domains/Bungee/api/fetchBungeeIntentQuoteStatus'
import { fetchSwapStateWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchSwapState'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { fetchTransaction } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransaction'
import { fetchSubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/fetchSubmittedUserOperation'
import { IndexedTransaction } from '@zeal/domains/Transactions'
import { fetchIndexedTransactions } from '@zeal/domains/Transactions/api/fetchIndexedTransactions'

const fetchTopUpFromIndexer = async ({
    transactionHash,
    startedAtMs,
    cardSafeAddress,
    signal,
}: {
    startedAtMs: number
    transactionHash: Hexadecimal.Hexadecimal
    cardSafeAddress: Web3.address.Address
    signal?: AbortSignal
}): Promise<IndexedTransaction | null> => {
    const transactions = await fetchIndexedTransactions({
        address: cardSafeAddress,
        networkHexId: CARD_NETWORK.hexChainId,
        afterTimestampMs: startedAtMs - 5 * 60 * 1000, // Add a buffer of 5 minutes to account for potential delays in indexing
        logsForAddresses: null,
        beforeTimestampMs: null,
        signal,
    })

    return transactions.find((tx) => tx.hash === transactionHash) || null
}

export const fetchSubmittedCardTopUp = async ({
    submittedCardTopUp,
    networkMap,
    networkRPCMap,
    signal,
}: {
    submittedCardTopUp: SubmittedCardTopUp
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<SubmittedCardTopUp> => {
    const { cardSafeAddress } = submittedCardTopUp

    switch (submittedCardTopUp.type) {
        case 'earn_eoa_eure_withdrawal':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_transaction': {
                    const submittedTx = await fetchTransaction({
                        transaction:
                            submittedCardTopUp.state.submittedTransaction,
                        networkRPCMap,
                        network: CARD_NETWORK,
                    })

                    switch (submittedTx.state) {
                        case 'queued':
                        case 'included_in_block':
                        case 'replaced':
                            return submittedCardTopUp
                        case 'failed':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_tx',
                                    transaction: submittedTx,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: {
                                        type: 'waiting_for_indexer_multiple_tx',
                                        finalTxHash: submittedTx.hash,
                                    },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedTx)
                    }
                }
                case 'waiting_for_indexer_multiple_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.finalTxHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_multiple_tx',
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                case 'failed_tx':
                case 'completed_with_multiple_tx':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }

        case 'send_eoa':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_transaction': {
                    const submittedTx = await fetchTransaction({
                        transaction:
                            submittedCardTopUp.state.submittedTransaction,
                        networkRPCMap,
                        network: CARD_NETWORK,
                    })

                    switch (submittedTx.state) {
                        case 'queued':
                        case 'included_in_block':
                        case 'replaced':
                            return submittedCardTopUp
                        case 'failed':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_tx',
                                    transaction: submittedTx,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: {
                                        type: 'waiting_for_indexer_single_tx',
                                        txHash: submittedTx.hash,
                                    },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedTx)
                    }
                }
                case 'waiting_for_indexer_single_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.txHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_single_tx',
                                  completedTxHash: indexedTx.hash,
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                case 'failed_tx':
                case 'completed_with_single_tx':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }

        case 'earn_safe_eure_withdrawal':
        case 'send_safe':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_user_operation': {
                    const submittedUserOp = await fetchSubmittedUserOperation({
                        submittedUserOperation:
                            submittedCardTopUp.state.submittedUserOperation,
                        networkRPCMap,
                        network: CARD_NETWORK,
                    })

                    switch (submittedUserOp.state) {
                        case 'bundled':
                        case 'pending':
                            return submittedCardTopUp
                        case 'failed':
                        case 'rejected':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_user_operation',
                                    userOperation: submittedUserOp,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: {
                                        type: 'waiting_for_indexer_single_tx',
                                        txHash: submittedUserOp.bundleTransactionHash,
                                    },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedUserOp)
                    }
                }
                case 'waiting_for_indexer_single_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.txHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_single_tx',
                                  completedTxHash: indexedTx.hash,
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                case 'failed_user_operation':
                case 'completed_with_single_tx':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        case 'swap_eoa_native_send_transaction': {
            const network = findNetworkByHexChainId(
                submittedCardTopUp.fromAmount.currency.networkHexChainId,
                networkMap
            )

            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_transaction': {
                    const submittedTx = await fetchTransaction({
                        transaction:
                            submittedCardTopUp.state.submittedTransaction,
                        networkRPCMap,
                        network,
                    })

                    switch (submittedTx.state) {
                        case 'queued':
                        case 'included_in_block':
                        case 'replaced':
                            return submittedCardTopUp
                        case 'failed':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_tx',
                                    transaction: submittedTx,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: { type: 'waiting_for_swap' },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedTx)
                    }
                }
                case 'waiting_for_swap':
                    switch (submittedCardTopUp.provider) {
                        case 'bungee': {
                            const swapState =
                                await fetchBungeeIntentQuoteStatus({
                                    requestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                    signal,
                                })

                            switch (swapState.type) {
                                case 'in_progress':
                                    return submittedCardTopUp
                                case 'cancelled':
                                case 'expired':
                                case 'refunded':
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: swapState.type,
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                case 'completed':
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state: {
                                                type: 'waiting_for_indexer_multiple_tx',
                                                finalTxHash:
                                                    swapState.destinationTxHash,
                                            },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                default:
                                    return notReachable(swapState)
                            }
                        }
                        case 'swaps_io':
                            const swapState = await fetchSwapStateWithRetries({
                                hash: submittedCardTopUp.quoteRequestHash,
                                from: submittedCardTopUp.fromAmount,
                                to: submittedCardTopUp.toAmount,
                                signal,
                            })

                            switch (swapState.state) {
                                case 'completed_liq_sent':
                                case 'completed_sent':
                                    // complete
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state: {
                                                type: 'waiting_for_indexer_multiple_tx',
                                                finalTxHash:
                                                    swapState.toTransactionHash,
                                            },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                case 'cancelled_awaiting_slash':
                                case 'awaiting_receive':
                                case 'awaiting_liq_send':
                                case 'awaiting_signature':
                                case 'awaiting_send':
                                    // in progress
                                    return submittedCardTopUp
                                case 'cancelled_no_slash':
                                case 'cancelled_slashed':
                                    // failed
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: 'cancelled',
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                default:
                                    return notReachable(swapState)
                            }

                        default:
                            return notReachable(submittedCardTopUp.provider)
                    }
                case 'waiting_for_indexer_multiple_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.finalTxHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_multiple_tx',
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }

                case 'completed_with_multiple_tx':
                case 'failed_tx':
                case 'failed_swap':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        case 'swap_safe_native_send_transaction': {
            const network = findNetworkByHexChainId(
                submittedCardTopUp.fromAmount.currency.networkHexChainId,
                networkMap
            )

            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_user_operation': {
                    const submittedUserOp = await fetchSubmittedUserOperation({
                        submittedUserOperation:
                            submittedCardTopUp.state.submittedUserOperation,
                        networkRPCMap,
                        network,
                    })

                    switch (submittedUserOp.state) {
                        case 'bundled':
                        case 'pending':
                            return submittedCardTopUp
                        case 'failed':
                        case 'rejected':
                            return {
                                ...submittedCardTopUp,
                                state: {
                                    type: 'failed_user_operation',
                                    userOperation: submittedUserOp,
                                },
                            }
                        case 'completed':
                            return await fetchSubmittedCardTopUp({
                                submittedCardTopUp: {
                                    ...submittedCardTopUp,
                                    state: { type: 'waiting_for_swap' },
                                },
                                networkMap,
                                networkRPCMap,
                                signal,
                            })
                        default:
                            return notReachable(submittedUserOp)
                    }
                }
                case 'waiting_for_swap':
                    switch (submittedCardTopUp.provider) {
                        case 'bungee': {
                            const swapState =
                                await fetchBungeeIntentQuoteStatus({
                                    requestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                    signal,
                                })

                            switch (swapState.type) {
                                case 'in_progress':
                                    return submittedCardTopUp
                                case 'cancelled':
                                case 'expired':
                                case 'refunded':
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: swapState.type,
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                case 'completed':
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state: {
                                                type: 'waiting_for_indexer_multiple_tx',
                                                finalTxHash:
                                                    swapState.destinationTxHash,
                                            },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })

                                default:
                                    return notReachable(swapState)
                            }
                        }
                        case 'swaps_io':
                            const swapState = await fetchSwapStateWithRetries({
                                hash: submittedCardTopUp.quoteRequestHash,
                                from: submittedCardTopUp.fromAmount,
                                to: submittedCardTopUp.toAmount,
                                signal,
                            })

                            switch (swapState.state) {
                                case 'completed_liq_sent':
                                case 'completed_sent':
                                    // complete
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state: {
                                                type: 'waiting_for_indexer_multiple_tx',
                                                finalTxHash:
                                                    swapState.toTransactionHash,
                                            },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                case 'cancelled_awaiting_slash':
                                case 'awaiting_receive':
                                case 'awaiting_liq_send':
                                case 'awaiting_signature':
                                case 'awaiting_send':
                                    // in progress
                                    return submittedCardTopUp
                                case 'cancelled_no_slash':
                                case 'cancelled_slashed':
                                    // failed
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: 'cancelled',
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                default:
                                    return notReachable(swapState)
                            }
                        default:
                            return notReachable(submittedCardTopUp.provider)
                    }
                case 'waiting_for_indexer_multiple_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.finalTxHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_multiple_tx',
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                case 'completed_with_multiple_tx':
                case 'failed_swap':
                case 'failed_user_operation':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        case 'earn_sign_typed_data':
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_swap':
                    switch (submittedCardTopUp.provider) {
                        case 'bungee': {
                            const swapState =
                                await fetchBungeeIntentQuoteStatus({
                                    requestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                    signal,
                                })

                            switch (swapState.type) {
                                case 'in_progress':
                                    return submittedCardTopUp
                                case 'cancelled':
                                case 'expired':
                                case 'refunded':
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: swapState.type,
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                case 'completed':
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state: {
                                                type: 'waiting_for_indexer_multiple_tx',
                                                finalTxHash:
                                                    swapState.destinationTxHash,
                                            },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                default:
                                    return notReachable(swapState)
                            }
                        }
                        case 'swaps_io':
                            // FIXME :: @Nicvaniek
                            throw new Error(`Not implemented`)
                        default:
                            return notReachable(submittedCardTopUp.provider)
                    }

                case 'waiting_for_indexer_multiple_tx':
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.finalTxHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_multiple_tx',
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }

                case 'completed_with_multiple_tx':
                case 'failed_swap':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        case 'swap_sign_typed_data': {
            switch (submittedCardTopUp.state.type) {
                case 'waiting_for_swap': {
                    switch (submittedCardTopUp.provider) {
                        case 'bungee': {
                            const swapState =
                                await fetchBungeeIntentQuoteStatus({
                                    requestHash:
                                        submittedCardTopUp.quoteRequestHash,
                                    signal,
                                })

                            switch (swapState.type) {
                                case 'in_progress':
                                    return submittedCardTopUp
                                case 'cancelled':
                                case 'expired':
                                case 'refunded':
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: swapState.type,
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                case 'completed':
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state:
                                                swapState.sourceTxHash ===
                                                swapState.destinationTxHash
                                                    ? {
                                                          type: 'waiting_for_indexer_single_tx',
                                                          txHash: swapState.destinationTxHash,
                                                      }
                                                    : {
                                                          type: 'waiting_for_indexer_multiple_tx',
                                                          finalTxHash:
                                                              swapState.destinationTxHash,
                                                      },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                default:
                                    return notReachable(swapState)
                            }
                        }
                        case 'swaps_io':
                            const swapState = await fetchSwapStateWithRetries({
                                hash: submittedCardTopUp.quoteRequestHash,
                                from: submittedCardTopUp.fromAmount,
                                to: submittedCardTopUp.toAmount,
                                signal,
                            })

                            switch (swapState.state) {
                                case 'completed_liq_sent':
                                case 'completed_sent':
                                    // complete
                                    return await fetchSubmittedCardTopUp({
                                        submittedCardTopUp: {
                                            ...submittedCardTopUp,
                                            state:
                                                !swapState.fromTransaction ||
                                                swapState.fromTransaction
                                                    ?.hash ===
                                                    swapState.toTransactionHash
                                                    ? {
                                                          type: 'waiting_for_indexer_single_tx',
                                                          txHash: swapState.toTransactionHash,
                                                      }
                                                    : {
                                                          type: 'waiting_for_indexer_multiple_tx',
                                                          finalTxHash:
                                                              swapState.toTransactionHash,
                                                      },
                                        },
                                        networkMap,
                                        networkRPCMap,
                                        signal,
                                    })
                                case 'cancelled_awaiting_slash':
                                case 'awaiting_receive':
                                case 'awaiting_liq_send':
                                case 'awaiting_signature':
                                case 'awaiting_send':
                                    // in progress
                                    return submittedCardTopUp
                                case 'cancelled_no_slash':
                                case 'cancelled_slashed':
                                    // failed
                                    return {
                                        ...submittedCardTopUp,
                                        state: {
                                            type: 'failed_swap',
                                            state: 'cancelled',
                                            quoteRequestHash:
                                                submittedCardTopUp.quoteRequestHash,
                                        },
                                    }
                                default:
                                    return notReachable(swapState)
                            }
                        default:
                            return notReachable(submittedCardTopUp.provider)
                    }
                }

                case 'waiting_for_indexer_multiple_tx': {
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.finalTxHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_multiple_tx',
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                }
                case 'waiting_for_indexer_single_tx': {
                    const indexedTx = await fetchTopUpFromIndexer({
                        transactionHash: submittedCardTopUp.state.txHash,
                        startedAtMs: submittedCardTopUp.startedAtMs,
                        cardSafeAddress,
                        signal,
                    })

                    return !indexedTx
                        ? submittedCardTopUp
                        : {
                              ...submittedCardTopUp,
                              state: {
                                  type: 'completed_with_single_tx',
                                  completedTxHash: indexedTx.hash,
                                  completedAtMs: indexedTx.timestamp,
                              },
                          }
                }

                case 'completed_with_multiple_tx':
                case 'completed_with_single_tx':
                case 'failed_swap':
                    return submittedCardTopUp
                default:
                    return notReachable(submittedCardTopUp.state)
            }
        }
        default:
            return notReachable(submittedCardTopUp)
    }
}

import { notReachable } from '@zeal/toolkit'

import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import {
    DaiPermitSignMessage,
    Permit2SignMessage,
    PermitSignMessage,
    SignMessageSimulationResult,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { fetchApprovalSpenderSaftyCheck } from '@zeal/domains/SafetyCheck/api/fetchApprovalSpenderSafetyCheckRequest'
import { fetchBlockaidSignRequestSafetyCheck } from '@zeal/domains/SafetyCheck/api/fetchBlockaidSignRequestSafetyCheck'
import {
    DappRadarInfo,
    fetchDappInfo,
} from '@zeal/domains/SafetyCheck/api/fetchDappInfo'
import { fetchTokenVerificationSafetyCheck } from '@zeal/domains/SafetyCheck/api/fetchTokenVerificationSafetyCheck'
import { calculateApprovalExpirationLimitCheck } from '@zeal/domains/SafetyCheck/helpers/calculateApprovalExpirationLimitCheck'
import { SmartContract } from '@zeal/domains/SmartContract'

import { parseSimulatedSignMessage } from '../parsers/parseSimulatedSignMessage'

type Params = {
    request: SignMessageRequest
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
    signal: AbortSignal
}

export type FetchSimulatedSignMessage = (
    params: Params
) => Promise<SignMessageSimulationResult>

const patchDappInfo = (
    input: Permit2SignMessage | PermitSignMessage | DaiPermitSignMessage,
    dappInfo: DappRadarInfo | null
): Permit2SignMessage | PermitSignMessage | DaiPermitSignMessage => {
    if (!dappInfo) {
        return input
    }
    const smartContract: SmartContract = {
        ...input.approveTo,
        logo: dappInfo.logo,
        name: dappInfo.name,
        website: dappInfo.website,
    }
    return {
        ...input,
        approveTo: smartContract,
    }
}
export const fetchSimulatedSignMessage = async ({
    network,
    request,
    dApp,
    networkMap,
    networkRPCMap,
    signal,
}: Params): Promise<SignMessageSimulationResult> => {
    const currencies = await fetchStaticCurrencies()
    const dappInfo = dApp
        ? await fetchDappInfo({ hostname: dApp.hostname || '' })
        : null
    try {
        switch (network.type) {
            case 'predefined':
                switch (request.method) {
                    case 'personal_sign':
                        return { type: 'not_supported' }
                    case 'eth_signTypedData':
                    case 'eth_signTypedData_v3':
                    case 'eth_signTypedData_v4':
                        const parsedMessage = parseSimulatedSignMessage({
                            input: request,
                            knownCurrencies: currencies,
                            networkMap,
                            now: Date.now(),
                        }).getSuccessResultOrThrow(
                            'issues in SignMessage parsing'
                        )
                        switch (parsedMessage.type) {
                            case 'UnknownSignMessage': {
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: await fetchBlockaidSignRequestSafetyCheck(
                                            {
                                                requestToCheck: request,
                                                dApp,
                                                signal,
                                                network,
                                            }
                                        ),
                                        currencies,
                                        message: parsedMessage,
                                    },
                                }
                            }
                            case 'DaiPermitSignMessage':
                            case 'PermitSignMessage': {
                                const [
                                    blockid,
                                    approval,
                                    tokenVerification,
                                    expirationChecks,
                                ] = await Promise.all([
                                    fetchBlockaidSignRequestSafetyCheck({
                                        requestToCheck: request,
                                        dApp,
                                        signal,
                                        network,
                                    }),
                                    fetchApprovalSpenderSaftyCheck({
                                        networkRPCMap,
                                        network,
                                        signal,
                                        spenderAddress:
                                            parsedMessage.approveTo.address,
                                    }),
                                    fetchTokenVerificationSafetyCheck({
                                        currencyIds: [
                                            parsedMessage.allowance.amount
                                                .amount.currency.id,
                                        ],
                                    }),
                                    calculateApprovalExpirationLimitCheck({
                                        allowance: parsedMessage.allowance,
                                    }),
                                ])
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: [
                                            ...blockid,
                                            approval,
                                            ...tokenVerification,
                                            expirationChecks,
                                        ],
                                        currencies,
                                        message: patchDappInfo(
                                            parsedMessage,
                                            dappInfo
                                        ),
                                    },
                                }
                            }
                            case 'Permit2SignMessage': {
                                const [
                                    blockid,
                                    approval,
                                    tokenVerification,
                                    expirationChecks,
                                ] = await Promise.all([
                                    fetchBlockaidSignRequestSafetyCheck({
                                        requestToCheck: request,
                                        dApp,
                                        signal,
                                        network,
                                    }),
                                    fetchApprovalSpenderSaftyCheck({
                                        signal,
                                        networkRPCMap,
                                        network,
                                        spenderAddress:
                                            parsedMessage.approveTo.address,
                                    }),
                                    fetchTokenVerificationSafetyCheck({
                                        currencyIds:
                                            parsedMessage.allowances.map(
                                                (allowance) =>
                                                    allowance.amount.amount
                                                        .currency.id
                                            ),
                                    }),
                                    parsedMessage.allowances.map((allowance) =>
                                        calculateApprovalExpirationLimitCheck({
                                            allowance,
                                        })
                                    ),
                                ])
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: [
                                            ...blockid,
                                            approval,
                                            ...tokenVerification,
                                            ...expirationChecks,
                                        ],

                                        currencies,
                                        message: patchDappInfo(
                                            parsedMessage,
                                            dappInfo
                                        ),
                                    },
                                }
                            }

                            /* istanbul ignore next */
                            default:
                                return notReachable(parsedMessage)
                        }

                    default:
                        return notReachable(request)
                }

            case 'custom':
            case 'testnet':
                return { type: 'not_supported' }

            default:
                return notReachable(network)
        }
    } catch (e) {
        captureError(e)
        return { type: 'failed' }
    }
}

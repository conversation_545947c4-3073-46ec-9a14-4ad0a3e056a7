import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { GasAbstractionTransactionFeeResponse } from '@zeal/domains/UserOperation'
import { fetchGasAbstractionTransactionFees } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFees'
import {
    DUMMY_EOA_SIGNATURE,
    DUMMY_PASSKEY_SIGNATURE,
    EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
    passkeySignerGasBufferConfig,
} from '@zeal/domains/UserOperation/constants'

import { NonSponsoredFooter } from './NonSponsoredFooter'
import { SponsoredFooter } from './SponsoredFooter'

type Props = {
    keyStore: Safe4337
    initialFeeForecast: GasAbstractionTransactionFeeResponse
    userOperationRequest: SimulatedUserOperationRequest
    simulation: SimulateTransactionResponse
    portfolio: ServerPortfolio2 | null
    gasCurrencyPresetMap: GasCurrencyPresetMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    installationId: string
    actionSource: ActionSource
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof NonSponsoredFooter> | MsgOf<typeof SponsoredFooter>

const isSponsored = (
    initialFeeForecast: GasAbstractionTransactionFeeResponse
): boolean => {
    switch (initialFeeForecast.type) {
        case 'sponsored_transaction':
            return true
        case 'non_sponsored_transaction':
            return false
        /* istanbul ignore next */
        default:
            return notReachable(initialFeeForecast)
    }
}

const POLLABLE_INTERVAL_MS = 10_000

export const Footer = ({
    onMsg,
    keyStore,
    initialFeeForecast,
    networkRPCMap,
    installationId,
    gasCurrencyPresetMap,
    simulation,
    userOperationRequest,
    portfolio,
    networkMap,
    defaultCurrencyConfig,
    actionSource,
}: Props) => {
    const [pollable] = useLoadedPollableData(
        fetchGasAbstractionTransactionFees,
        {
            type: 'loaded',
            data: initialFeeForecast,
            params: (() => {
                switch (userOperationRequest.type) {
                    case 'simulated_safe_deployment_bundle_user_operation_request':
                    case 'simulated_safe_with_add_owner_user_operation_request':
                        return {
                            initCode: userOperationRequest.initCode,
                            network: userOperationRequest.network,
                            sender: userOperationRequest.account.address,
                            metaTransactionDatas: [
                                ...userOperationRequest.metaTransactionDatas,
                                userOperationRequest.addOwnerMetaTransactionData,
                            ],
                            networkRPCMap,
                            callGasLimitBuffer: 0n,
                            verificationGasLimitBuffer:
                                passkeySignerGasBufferConfig[
                                    keyStore.safeDeplymentConfig.passkeyOwner
                                        .signerVersion
                                ].verificationGasLimitBuffer,
                            entrypoint: userOperationRequest.entrypoint,
                            serverPortfolio: portfolio,
                            sponsored: isSponsored(initialFeeForecast),
                            dummySignature: DUMMY_PASSKEY_SIGNATURE,
                            defaultCurrencyConfig,
                            installationId,
                            networkMap,
                            actionSource,
                        }
                    case 'simulated_safe_without_deployment_bundle_user_operation_request':
                        return {
                            initCode: userOperationRequest.initCode,
                            network: userOperationRequest.network,
                            sender: userOperationRequest.account.address,
                            metaTransactionDatas:
                                userOperationRequest.metaTransactionDatas,
                            networkRPCMap,
                            callGasLimitBuffer: 0n,
                            verificationGasLimitBuffer:
                                EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
                            entrypoint: userOperationRequest.entrypoint,
                            serverPortfolio: portfolio,
                            sponsored: isSponsored(initialFeeForecast),
                            dummySignature: DUMMY_EOA_SIGNATURE,
                            defaultCurrencyConfig,
                            installationId,
                            networkMap,
                            actionSource,
                        }

                    default:
                        return notReachable(userOperationRequest)
                }
            })(),
        },
        { pollIntervalMilliseconds: POLLABLE_INTERVAL_MS, stopIf: () => false }
    )
    const [pollingStartedAt, setPollingStartedAt] = useState<number>(Date.now)

    useEffect(() => {
        setPollingStartedAt(Date.now())
    }, [pollable])

    switch (pollable.data.type) {
        case 'sponsored_transaction':
            return (
                <SponsoredFooter
                    onMsg={onMsg}
                    simulation={simulation}
                    userOperationRequest={userOperationRequest}
                    selectedFee={pollable.data.fee}
                />
            )
        case 'non_sponsored_transaction':
            return (
                <NonSponsoredFooter
                    installationId={installationId}
                    userOperationRequest={userOperationRequest}
                    simulation={simulation}
                    fees={pollable.data.fees}
                    portfolio={portfolio}
                    pollingInterval={POLLABLE_INTERVAL_MS}
                    pollingStartedAt={pollingStartedAt}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(pollable.data)
    }
}

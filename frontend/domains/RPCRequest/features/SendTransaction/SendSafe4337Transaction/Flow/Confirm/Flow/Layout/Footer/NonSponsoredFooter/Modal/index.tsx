import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Result } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Network } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { NonSponsoredGasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import { GasCurrencySelector } from './GasCurrencySelector'

import { FeeForecastError } from '../validation'

type Props = {
    state: State
    account: Account
    network: Network
    pollingInterval: number
    pollingStartedAt: number
    fees: NonSponsoredGasAbstractionTransactionFee[]
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolio: ServerPortfolio2 | null
    feeForecastValidation: Result<
        FeeForecastError,
        NonSponsoredGasAbstractionTransactionFee
    >
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof GasCurrencySelector>

export type State = { type: 'closed' } | { type: 'gas_currency_selector' }

export const Modal = ({
    state,
    onMsg,
    pollingInterval,
    pollingStartedAt,
    feeForecastValidation,
    fees,
    portfolio,
    account,
    network,
    gasCurrencyPresetMap,
    installationId,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'gas_currency_selector':
            return (
                <UIModal>
                    <GasCurrencySelector
                        installationId={installationId}
                        pollingStartedAt={pollingStartedAt}
                        pollingInterval={pollingInterval}
                        feeForecastValidation={feeForecastValidation}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        account={account}
                        network={network}
                        fees={fees}
                        portfolio={portfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}

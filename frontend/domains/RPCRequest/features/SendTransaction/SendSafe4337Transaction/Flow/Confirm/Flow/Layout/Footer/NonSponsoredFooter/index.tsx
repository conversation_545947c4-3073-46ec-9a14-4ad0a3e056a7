import { useEffect, useMemo, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { CryptoCurrency, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { NonSponsoredGasAbstractionTransactionFee } from '@zeal/domains/UserOperation'
import { calculateSelectedGasCurrency } from '@zeal/domains/UserOperation/helpers/calculateSelectedGasCurrency'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'
import { getOutgoingAmounts, validate } from './validation'

type Props = {
    userOperationRequest: SimulatedUserOperationRequest
    simulation: SimulateTransactionResponse
    fees: NonSponsoredGasAbstractionTransactionFee[]
    portfolio: ServerPortfolio2 | null
    pollingInterval: number
    pollingStartedAt: number
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_user_confirmation_requested'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_submit_click'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_minimize_click'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

export const NonSponsoredFooter = ({
    portfolio,
    fees,
    onMsg,
    userOperationRequest,
    gasCurrencyPresetMap,
    pollingStartedAt,
    pollingInterval,
    simulation,
    installationId,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const savedGasCurrencyId =
        gasCurrencyPresetMap[userOperationRequest.network.hexChainId] || null

    const outgoingAmounts = useMemo(
        () =>
            getOutgoingAmounts({
                simulatedTransaction: simulation.transaction,
            }),
        [simulation.transaction]
    )

    const [selectedGasCurrency, setSelectedGasCurrency] =
        useState<CryptoCurrency>(
            calculateSelectedGasCurrency({
                serverPortfolio: portfolio,
                fees,
                savedGasCurrencyId,
                outgoingAmounts,
            })
        )

    useEffect(() => {
        setSelectedGasCurrency(
            calculateSelectedGasCurrency({
                serverPortfolio: portfolio,
                fees,
                savedGasCurrencyId,
                outgoingAmounts,
            })
        )
    }, [
        portfolio,
        fees,
        savedGasCurrencyId,
        outgoingAmounts,
        simulation.transaction,
    ])

    const validationResult = validate({
        fees,
        portfolio,
        selectedGasCurrency,
        simulatedTransaction: simulation.transaction,
    })

    return (
        <>
            <Layout
                userOperationRequest={userOperationRequest}
                simulation={simulation}
                fees={fees}
                selectedGasCurrency={selectedGasCurrency}
                portfolio={portfolio}
                pollingInterval={pollingInterval}
                pollingStartedAt={pollingStartedAt}
                feeForecastValidation={validationResult}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_fee_forecast_click':
                            setModal({ type: 'gas_currency_selector' })
                            break
                        case 'on_user_confirmation_requested':
                        case 'on_cancel_confirm_transaction_clicked':
                        case 'on_submit_click':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                installationId={installationId}
                state={modal}
                account={userOperationRequest.account}
                network={userOperationRequest.network}
                pollingInterval={pollingInterval}
                pollingStartedAt={pollingStartedAt}
                fees={fees}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                portfolio={portfolio}
                feeForecastValidation={validationResult}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_minimize_click':
                            onMsg(msg)
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break
                        case 'on_4337_gas_currency_selected':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            setSelectedGasCurrency(msg.selectedGasCurrency)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}

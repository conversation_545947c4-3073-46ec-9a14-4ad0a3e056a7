import { FormattedMessage } from 'react-intl'
import { SectionListData } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Rocket } from '@zeal/uikit/Icon/Rocket'
import { ListItem } from '@zeal/uikit/ListItem'
import { ProgressSpinner } from '@zeal/uikit/ProgressSpinner'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { SectionList } from '@zeal/uikit/SectionList'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { Result } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { CryptoCurrency, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Avatar } from '@zeal/domains/Currency/components/Avatar'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { Network } from '@zeal/domains/Network'
import { Avatar as NetworkAvatar } from '@zeal/domains/Network/components/Avatar'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import { NonSponsoredGasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import { FeeForecastError } from '../validation'

type Props = {
    account: Account
    network: Network
    feeForecastValidation: Result<
        FeeForecastError,
        NonSponsoredGasAbstractionTransactionFee
    >
    fees: NonSponsoredGasAbstractionTransactionFee[]
    portfolio: ServerPortfolio2 | null
    gasCurrencyPresetMap: GasCurrencyPresetMap
    pollingInterval: number
    pollingStartedAt: number
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_minimize_click' }
    | { type: 'on_4337_auto_gas_token_selection_clicked'; network: Network }
    | {
          type: 'on_4337_gas_currency_selected'
          selectedGasCurrency: CryptoCurrency
      }

const calculateFeeOptionState = (
    feeOption: NonSponsoredGasAbstractionTransactionFee,
    feeForecastValidation: Result<
        FeeForecastError,
        NonSponsoredGasAbstractionTransactionFee
    >
): 'error' | 'normal' => {
    switch (feeForecastValidation.type) {
        case 'Failure':
            switch (feeForecastValidation.reason.type) {
                case 'insufficient_gas_token_balance':
                    return feeForecastValidation.reason.selectedFee
                        .feeInTokenCurrency.currency.id ===
                        feeOption.feeInTokenCurrency.currency.id
                        ? 'error'
                        : 'normal'
                /* istanbul ignore next */
                default:
                    return notReachable(feeForecastValidation.reason.type)
            }
        case 'Success':
            return 'normal'
        /* istanbul ignore next */
        default:
            return notReachable(feeForecastValidation)
    }
}

export const GasCurrencySelector = ({
    portfolio,
    fees,
    account,
    network,
    feeForecastValidation,
    gasCurrencyPresetMap,
    pollingStartedAt,
    pollingInterval,
    installationId,
    onMsg,
}: Props) => {
    const selectedFee = (() => {
        switch (feeForecastValidation.type) {
            case 'Failure':
                return feeForecastValidation.reason.selectedFee
            case 'Success':
                return feeForecastValidation.data
            /* istanbul ignore next */
            default:
                return notReachable(feeForecastValidation)
        }
    })()

    const autoGasTokenSelectionEnabled =
        !gasCurrencyPresetMap[network.hexChainId]

    const portfolioFees = portfolio
        ? fees.filter((fee) =>
              portfolio.tokens
                  .map((t) => t.balance.currency.id)
                  .includes(fee.feeInTokenCurrency.currency.id)
          )
        : fees

    const nonPortfolioFees = fees.filter((fee) => !portfolioFees.includes(fee))

    const sections: SectionListData<NonSponsoredGasAbstractionTransactionFee>[] =
        [
            {
                data: portfolioFees,
            },
            {
                data: nonPortfolioFees,
            },
        ]

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12}>
                <ActionBar
                    top={<ActionBarAccountIndicator account={account} />}
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="GasCurrencySelector.networkFee"
                                        defaultMessage="Network fee"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <NetworkAvatar
                            currentNetwork={{
                                type: 'specific_network',
                                network,
                            }}
                            size={24}
                        />
                    }
                />

                <Column spacing={0}>
                    <Group variant="default">
                        <ListItem
                            variant="default"
                            size="regular"
                            onClick={() => {
                                onMsg({
                                    type: 'on_4337_auto_gas_token_selection_clicked',
                                    network,
                                })
                            }}
                            aria-current={autoGasTokenSelectionEnabled}
                            avatar={({ size }) => (
                                <Rocket size={size} color="iconAccent2" />
                            )}
                            primaryText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.title"
                                    defaultMessage="Automatic fee handling"
                                />
                            }
                            shortText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.description"
                                    defaultMessage="Pay fees from the largest balance"
                                />
                            }
                        />
                    </Group>
                </Column>
                <Column spacing={12} fill shrink>
                    <Column spacing={4} shrink>
                        <Row spacing={0}>
                            <Text
                                variant="paragraph"
                                color="textSecondary"
                                weight="regular"
                            >
                                <FormattedMessage
                                    id="GasCurrencySelector.payNetworkFeesUsing"
                                    defaultMessage="Pay network fees using"
                                />
                            </Text>
                            <Spacer />
                            <ProgressSpinner
                                key={pollingStartedAt}
                                size={20}
                                durationMs={pollingInterval}
                            />
                        </Row>

                        <SectionList
                            variant="grouped"
                            itemSpacing={8}
                            sectionSpacing={8}
                            sections={sections}
                            renderItem={({ item: feeOption }) => (
                                <FeeOptionListItem
                                    state={calculateFeeOptionState(
                                        feeOption,
                                        feeForecastValidation
                                    )}
                                    portfolio={portfolio}
                                    key={
                                        feeOption.feeInTokenCurrency.currency.id
                                    }
                                    fee={feeOption}
                                    aria-current={
                                        autoGasTokenSelectionEnabled
                                            ? false
                                            : selectedFee.feeInTokenCurrency
                                                  .currency.id ===
                                              feeOption.feeInTokenCurrency
                                                  .currency.id
                                    }
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'GasCurrencySelectedEvent',
                                            installationId,
                                            networkHexId: network.hexChainId,
                                            currencyId:
                                                feeOption.feeInTokenCurrency
                                                    .currency.id,
                                        })
                                        return onMsg({
                                            type: 'on_4337_gas_currency_selected',
                                            selectedGasCurrency:
                                                feeOption.feeInTokenCurrency
                                                    .currency,
                                        })
                                    }}
                                />
                            )}
                        />
                    </Column>

                    <Spacer />

                    <Actions variant="default">
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            <FormattedMessage
                                id="GasCurrencySelector.save"
                                defaultMessage="Save"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const FeeOptionListItem = ({
    fee,
    portfolio,
    'aria-current': ariaCurrent,
    onClick,
    state,
}: {
    'aria-current': boolean
    fee: NonSponsoredGasAbstractionTransactionFee
    portfolio: ServerPortfolio2 | null
    state: 'normal' | 'error'
    onClick: () => void
}) => {
    const tokenCurrency = fee.feeInTokenCurrency.currency

    const balance = portfolio
        ? getBalanceByCryptoCurrency2({
              currency: tokenCurrency,
              serverPortfolio: portfolio,
          })
        : {
              amount: 0n,
              currency: tokenCurrency,
          }

    const shortTextColor = (() => {
        switch (state) {
            case 'normal':
                return 'textSecondary'
            case 'error':
                return 'textError'

            default:
                return notReachable(state)
        }
    })()

    return (
        <ListItem
            size="large"
            aria-current={ariaCurrent}
            primaryText={tokenCurrency.code}
            onClick={onClick}
            avatar={({ size }) => (
                <Avatar
                    rightBadge={() => null}
                    size={size}
                    currency={tokenCurrency}
                />
            )}
            shortText={
                <Text color={shortTextColor}>
                    <FormattedMessage
                        id="GasCurrencySelector.balance"
                        defaultMessage="Balance: {balance}"
                        values={{
                            balance: (
                                <FormattedMoneyPrecise
                                    withSymbol={false}
                                    sign={null}
                                    money={balance}
                                />
                            ),
                        }}
                    />
                </Text>
            }
            side={{
                title: (
                    <FormattedMoneyPrecise
                        withSymbol={false}
                        sign={null}
                        money={fee.feeInTokenCurrency}
                    />
                ),
                subtitle: fee.feeInDefaultCurrency ? (
                    <FormattedMoneyCompact money={fee.feeInDefaultCurrency} />
                ) : (
                    ' ' // non-breaking-space thing to keep right side at right place
                ),
            }}
        />
    )
}

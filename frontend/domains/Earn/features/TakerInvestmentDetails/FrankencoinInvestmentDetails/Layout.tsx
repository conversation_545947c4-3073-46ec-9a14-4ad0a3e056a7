import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { StackedBarChart } from '@zeal/uikit/Chart'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { ReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { FIAT_DUST } from '@zeal/domains/Currency/constants'
import {
    CHFEarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { TakerAPYTitle } from '@zeal/domains/Earn/components/TakerAPYTitle'
import { TakerHistoricalReturnsChart } from '@zeal/domains/Earn/components/TakerHistoricalReturnsChart'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import {
    FRANKENCOIN_MONITORING_URL,
    FRANKENCOIN_URL,
} from '@zeal/domains/Earn/constants'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { MetaDataListItem } from '../MetaDataListItem'

type Msg =
    | { type: 'close' }
    | {
          type: 'on_earn_deposit_asset_click'
          taker: Taker
      }
    | {
          type: 'on_earn_withdrawal_asset_click'
          taker: Taker
      }
    | { type: 'on_base_currency_info_click' }
    | {
          type: 'on_copy_taker_address_clicked'
          address: Web3.address.Address
      }

type Props = {
    variant: 'just_information' | 'information_and_interaction'
    taker: Taker
    takerApyMap: TakerApyMap
    takerPortfolioMap: TakerPortfolioMap2
    takerMetricsLoadable: ReloadableData<CHFEarnTakerMetrics, unknown>
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

export const Layout = ({
    variant,
    taker,
    takerApyMap,
    takerPortfolioMap,
    takerMetricsLoadable,
    defaultCurrencyConfig,
    installationId,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const { formattedFiatMoneyShort } = useMoneyFormat()

    return (
        <Screen
            padding="controller_tabs_fullscreen_scroll"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={8} fill shrink>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />

                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                    id="accounts-layout-label"
                                >
                                    <TakerTitle takerType={taker.type} />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />
                <ScrollContainer withFloatingActions>
                    <Column spacing={24}>
                        <Column spacing={16}>
                            <TakerAPYTitle
                                takerApyMap={takerApyMap}
                                taker={taker}
                            />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                {(() => {
                                    switch (taker.state) {
                                        case 'not_deployed':
                                            return (
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.chf.description"
                                                    defaultMessage="Earn interest on your CHF by depositing zCHF into Frankencoin - a trusted digital money market. Interest is generated from low-risk, overcollateralised loans on Frankencoin and paid in real-time. Your funds stay safe in a secure sub-account that only you control."
                                                />
                                            )
                                        case 'deployed':
                                            return (
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.chf.description.with_address_link"
                                                    defaultMessage="Earn interest on your CHF by depositing zCHF into Frankencoin - a trusted digital money market. Interest is generated from low-risk, overcollateralised loans on Frankencoin and paid in real-time. Your funds stay safe in a secure sub-account <link>(copy 0x)</link> that only you control."
                                                    values={{
                                                        link: (msg) => (
                                                            <TextButton
                                                                onClick={() =>
                                                                    onMsg({
                                                                        type: 'on_copy_taker_address_clicked',
                                                                        address:
                                                                            taker.address,
                                                                    })
                                                                }
                                                            >
                                                                {msg}
                                                            </TextButton>
                                                        ),
                                                    }}
                                                />
                                            )
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(taker)
                                    }
                                })()}
                            </Text>
                        </Column>
                        <TakerHistoricalReturnsChart taker={taker} />
                        <Column spacing={12}>
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="gray5"
                            >
                                <FormattedMessage
                                    id="earn-taker-investment-details.key-facts"
                                    defaultMessage="Key facts"
                                />
                            </Text>
                            <Column spacing={4}>
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.projected-yield"
                                            defaultMessage="Projected annual yield"
                                        />
                                    }
                                    rightText={getFormattedPercentage(
                                        takerApyMap[taker.type]
                                    )}
                                />
                                <MetaDataListItem
                                    leftText={
                                        <Tertiary
                                            color="on_light_bold"
                                            size="large"
                                            onClick={() => {
                                                postUserEvent({
                                                    type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                                    asset: 'chf',
                                                    tooltip: 'base_currency',
                                                    installationId,
                                                })
                                                onMsg({
                                                    type: 'on_base_currency_info_click',
                                                })
                                            }}
                                        >
                                            {({
                                                textVariant,
                                                textWeight,
                                                color,
                                            }) => (
                                                <Row spacing={4}>
                                                    <Text
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                        color={color}
                                                    >
                                                        <FormattedMessage
                                                            id="earn-taker-investment-details.base_currency.label"
                                                            defaultMessage="Base currency"
                                                        />
                                                    </Text>
                                                    <InfoCircleOutline
                                                        size={16}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </Tertiary>
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.chf.label"
                                            defaultMessage="Digital Swiss Franc"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.issuer"
                                            defaultMessage="Issuer"
                                        />
                                    }
                                    rightText={
                                        <Tertiary
                                            color="on_light_bold"
                                            size="large"
                                            onClick={() =>
                                                openExternalURL(FRANKENCOIN_URL)
                                            }
                                        >
                                            {({
                                                textVariant,
                                                textWeight,
                                                color,
                                            }) => (
                                                <Row spacing={4}>
                                                    <Text
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                        color={color}
                                                    >
                                                        Frankencoin
                                                    </Text>
                                                    <ExternalLink
                                                        size={16}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </Tertiary>
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.asset-class"
                                            defaultMessage="Asset class"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.fixed-income"
                                            defaultMessage="Fixed income"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.liquidity"
                                            defaultMessage="Liquidity"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.withdraw-anytime"
                                            defaultMessage="Withdraw anytime"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.yield"
                                            defaultMessage="Yield"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.accrued-realtime"
                                            defaultMessage="Accrued in real-time"
                                        />
                                    }
                                />
                            </Column>
                            <Column spacing={12}>
                                <Row
                                    spacing={0}
                                    alignX="stretch"
                                    alignY="baseline"
                                >
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="gray5"
                                    >
                                        <FormattedMessage
                                            id="earn-taker-investment-details.asset-coverage-ratio"
                                            defaultMessage="Asset coverage ratio"
                                        />
                                    </Text>
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="gray5"
                                    >
                                        {(() => {
                                            switch (takerMetricsLoadable.type) {
                                                case 'loading':
                                                case 'error':
                                                    return (
                                                        <Skeleton
                                                            variant="default"
                                                            height={18}
                                                            width={45}
                                                        />
                                                    )
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                    return `${takerMetricsLoadable.data.assetCoverageRatio.toFixed(
                                                        2
                                                    )}x`
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        takerMetricsLoadable
                                                    )
                                            }
                                        })()}
                                    </Text>
                                </Row>
                                <Column spacing={4}>
                                    <MetaDataListItem
                                        leftText={
                                            <FormattedMessage
                                                id="earn-taker-investment-details.total-zchf-supply"
                                                defaultMessage="Total ZCHF supply"
                                            />
                                        }
                                        rightText={(() => {
                                            switch (takerMetricsLoadable.type) {
                                                case 'loading':
                                                case 'error':
                                                    return (
                                                        <Skeleton
                                                            variant="default"
                                                            height={18}
                                                            width={45}
                                                        />
                                                    )
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                    return (
                                                        <FormattedMoneyCompact
                                                            money={
                                                                takerMetricsLoadable
                                                                    .data
                                                                    .totalSupply
                                                            }
                                                        />
                                                    )
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        takerMetricsLoadable
                                                    )
                                            }
                                        })()}
                                    />
                                    <MetaDataListItem
                                        leftText={
                                            <FormattedMessage
                                                id="earn-taker-investment-details.total-collateral"
                                                defaultMessage="Total Collateral"
                                            />
                                        }
                                        rightText={(() => {
                                            switch (takerMetricsLoadable.type) {
                                                case 'loading':
                                                case 'error':
                                                    return (
                                                        <Skeleton
                                                            variant="default"
                                                            height={18}
                                                            width={45}
                                                        />
                                                    )
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                    return (
                                                        <FormattedMoneyCompact
                                                            money={
                                                                takerMetricsLoadable
                                                                    .data
                                                                    .totalCollateral
                                                            }
                                                        />
                                                    )
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        takerMetricsLoadable
                                                    )
                                            }
                                        })()}
                                    />
                                </Column>
                                <Tertiary
                                    color="on_light"
                                    size="regular"
                                    onClick={() =>
                                        openExternalURL(FRANKENCOIN_URL)
                                    }
                                >
                                    {({ color, textVariant, textWeight }) => (
                                        <Row spacing={4}>
                                            <Text
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                            >
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.see-realtime"
                                                    defaultMessage="See real-time data"
                                                />
                                            </Text>
                                            <ExternalLink
                                                size={16}
                                                color={color}
                                            />
                                        </Row>
                                    )}
                                </Tertiary>
                            </Column>
                            <Column spacing={12}>
                                {(() => {
                                    switch (takerMetricsLoadable.type) {
                                        case 'loading':
                                        case 'error':
                                            return (
                                                <StackedBarChart.Skeleton
                                                    title={
                                                        <FormattedMessage
                                                            id="earn-taker-investment-details.collateral-composition"
                                                            defaultMessage="Collateral composition"
                                                        />
                                                    }
                                                />
                                            )
                                        case 'loaded':
                                        case 'reloading':
                                        case 'subsequent_failed':
                                            const collateral =
                                                takerMetricsLoadable.data
                                                    .collateralComposition
                                            const sortedSegments = [
                                                {
                                                    id: 'bitcoin' as const,
                                                    label: formatMessage({
                                                        id: 'collateral.bitcoin',
                                                        defaultMessage:
                                                            'Bitcoin',
                                                    }),
                                                    color: 'orange30' as const,
                                                    value: unsafe_toNumberWithFraction(
                                                        collateral.bitcoin
                                                            .amount,
                                                        collateral.bitcoin
                                                            .currency.fraction
                                                    ),
                                                    displayValue:
                                                        formattedFiatMoneyShort(
                                                            {
                                                                money: collateral.bitcoin,
                                                            }
                                                        ),
                                                },
                                                {
                                                    id: 'ethereum' as const,
                                                    label: formatMessage({
                                                        id: 'collateral.ethereum',
                                                        defaultMessage:
                                                            'Ethereum',
                                                    }),
                                                    color: 'blue30' as const,
                                                    value: unsafe_toNumberWithFraction(
                                                        collateral.ethereum
                                                            .amount,
                                                        collateral.ethereum
                                                            .currency.fraction
                                                    ),
                                                    displayValue:
                                                        formattedFiatMoneyShort(
                                                            {
                                                                money: collateral.ethereum,
                                                            }
                                                        ),
                                                },
                                                {
                                                    id: 'real-world-assets' as const,
                                                    label: formatMessage({
                                                        id: 'collateral.rwa',
                                                        defaultMessage:
                                                            'Real World Assets',
                                                    }),
                                                    color: 'purple50' as const,
                                                    value: unsafe_toNumberWithFraction(
                                                        collateral
                                                            .realWorldAssets
                                                            .amount,
                                                        collateral
                                                            .realWorldAssets
                                                            .currency.fraction
                                                    ),
                                                    displayValue:
                                                        formattedFiatMoneyShort(
                                                            {
                                                                money: collateral.realWorldAssets,
                                                            }
                                                        ),
                                                },
                                                {
                                                    id: 'other-assets' as const,
                                                    label: formatMessage({
                                                        id: 'collateral.other-assets',
                                                        defaultMessage:
                                                            'Other Assets',
                                                    }),
                                                    color: 'blue70' as const,
                                                    value: unsafe_toNumberWithFraction(
                                                        collateral.other.amount,
                                                        collateral.other
                                                            .currency.fraction
                                                    ),
                                                    displayValue:
                                                        formattedFiatMoneyShort(
                                                            {
                                                                money: collateral.other,
                                                            }
                                                        ),
                                                },
                                            ].sort((a, b) => b.value - a.value)
                                            return (
                                                <StackedBarChart
                                                    title={
                                                        <FormattedMessage
                                                            id="earn-taker-investment-details.collateral-composition"
                                                            defaultMessage="Collateral composition"
                                                        />
                                                    }
                                                    segments={sortedSegments}
                                                />
                                            )
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                takerMetricsLoadable
                                            )
                                    }
                                })()}
                                <Tertiary
                                    color="on_light"
                                    size="regular"
                                    onClick={() =>
                                        openExternalURL(
                                            FRANKENCOIN_MONITORING_URL
                                        )
                                    }
                                >
                                    {({ color, textVariant, textWeight }) => (
                                        <Row spacing={4}>
                                            <Text
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                            >
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.see-realtime"
                                                    defaultMessage="See real-time data"
                                                />
                                            </Text>
                                            <ExternalLink
                                                size={16}
                                                color={color}
                                            />
                                        </Row>
                                    )}
                                </Tertiary>
                            </Column>
                        </Column>
                    </Column>
                </ScrollContainer>
                {(() => {
                    switch (variant) {
                        case 'just_information':
                            return null
                        case 'information_and_interaction':
                            return (
                                <Actions variant="overlay">
                                    {sumTakerPortfolio({
                                        taker,
                                        takerPortfolioMap,
                                        defaultCurrencyConfig,
                                    }).amount > FIAT_DUST && (
                                        <Button
                                            onClick={() =>
                                                onMsg({
                                                    type: 'on_earn_withdrawal_asset_click',
                                                    taker,
                                                })
                                            }
                                            variant="secondary"
                                            size="regular"
                                        >
                                            <FormattedMessage
                                                id="withdraw"
                                                defaultMessage="Withdraw"
                                            />
                                        </Button>
                                    )}
                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            postUserEvent({
                                                type: 'EarnDepositButtonClickedEvent',
                                                asset: 'usd',
                                                location:
                                                    'investment_details_screen',
                                                installationId,
                                            })
                                            onMsg({
                                                type: 'on_earn_deposit_asset_click',
                                                taker,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="deposit"
                                            defaultMessage="Deposit"
                                        />
                                    </Button>
                                </Actions>
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(variant)
                    }
                })()}
            </Column>
        </Screen>
    )
}

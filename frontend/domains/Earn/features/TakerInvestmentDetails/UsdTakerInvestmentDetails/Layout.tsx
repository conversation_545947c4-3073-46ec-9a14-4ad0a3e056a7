import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { StackedBarChart } from '@zeal/uikit/Chart'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { ReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { FIAT_DUST } from '@zeal/domains/Currency/constants'
import {
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
    UsdEarnTakerMetrics,
} from '@zeal/domains/Earn'
import { TakerAPYTitle } from '@zeal/domains/Earn/components/TakerAPYTitle'
import { TakerHistoricalReturnsChart } from '@zeal/domains/Earn/components/TakerHistoricalReturnsChart'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import {
    EARN_USD_TAKER_USER_CURRENCY,
    SKY_COLLATERAL_URL,
    SKY_URL,
    SKY_ZEAL_FAQ_URL,
} from '@zeal/domains/Earn/constants'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { MetaDataListItem } from '../MetaDataListItem'

type Props = {
    variant: 'just_information' | 'information_and_interaction'
    taker: Taker
    takerApyMap: TakerApyMap
    takerPortfolioMap: TakerPortfolioMap2
    installationId: string

    defaultCurrencyConfig: DefaultCurrencyConfig
    takerMetricsLoadable: ReloadableData<UsdEarnTakerMetrics, undefined>
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_earn_deposit_asset_click'; taker: Taker }
    | { type: 'on_earn_withdrawal_asset_click'; taker: Taker }
    | { type: 'on_base_currency_info_click' }
    | { type: 'on_lending_operations_info_click' }
    | { type: 'on_market_making_operations_info_click' }
    | { type: 'on_treasury_operations_info_click' }
    | { type: 'on_faq_how_is_backed_clicked' }
    | { type: 'on_faq_can_lose_principal_clicked' }
    | { type: 'on_faq_high_returns_clicked' }
    | { type: 'on_faq_ftx_difference_clicked' }
    | { type: 'on_faq_insurance_clicked' }
    | { type: 'on_copy_taker_address_clicked'; address: Web3.address.Address }

const INCOME_SOURCES_2024 = {
    lendingOperations: {
        amount: 130000000000000000000000000n,
        currency: EARN_USD_TAKER_USER_CURRENCY,
    },
    marketMakingOperations: {
        amount: 129000000000000000000000000n,
        currency: EARN_USD_TAKER_USER_CURRENCY,
    },
    treasuryOperations: {
        amount: 58000000000000000000000000n,
        currency: EARN_USD_TAKER_USER_CURRENCY,
    },
}

export const Layout = ({
    variant,
    taker,
    takerApyMap,
    takerPortfolioMap,
    installationId,
    takerMetricsLoadable,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const { formattedFiatMoneyShort } = useMoneyFormat()

    return (
        <Screen
            padding="controller_tabs_fullscreen_scroll"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={8} fill shrink>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />

                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                    id="accounts-layout-label"
                                >
                                    <TakerTitle takerType={taker.type} />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />
                <ScrollContainer withFloatingActions>
                    <Column spacing={24}>
                        <Column spacing={16}>
                            <TakerAPYTitle
                                takerApyMap={takerApyMap}
                                taker={taker}
                            />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                {(() => {
                                    switch (taker.state) {
                                        case 'not_deployed':
                                            return (
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.usd.description"
                                                    defaultMessage="Sky is a digital money market offering stable, US dollar-denominated yields from short-duration U.S. Treasuries and overcollateralised lending—with no crypto volatility, 24/7 fund access, and transparent, onchain backing."
                                                />
                                            )
                                        case 'deployed':
                                            return (
                                                <FormattedMessage
                                                    id="earn-taker-investment-details.usd.description.with_address_link"
                                                    defaultMessage="Sky is a digital money market offering stable, US dollar-denominated yields from short-duration U.S. Treasuries and overcollateralised lending—with no crypto volatility, 24/7 fund access, and transparent, onchain backing. Investments are in a sub-account <link>(copy 0x)</link> controlled by you."
                                                    values={{
                                                        link: (msg) => (
                                                            <TextButton
                                                                onClick={() =>
                                                                    onMsg({
                                                                        type: 'on_copy_taker_address_clicked',
                                                                        address:
                                                                            taker.address,
                                                                    })
                                                                }
                                                            >
                                                                {msg}
                                                            </TextButton>
                                                        ),
                                                    }}
                                                />
                                            )
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(taker)
                                    }
                                })()}
                            </Text>
                        </Column>
                        <TakerHistoricalReturnsChart taker={taker} />
                        <StackedBarChart
                            title={
                                <FormattedMessage
                                    id="earn-taker-investment-details.usd.income-sources"
                                    defaultMessage="Income sources 2024"
                                />
                            }
                            onLegendClick={({ id }) => {
                                switch (id) {
                                    case 'lending_operations':
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'lending_operations',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_lending_operations_info_click',
                                        })
                                        break
                                    case 'market_making_operations':
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'market_making_operations',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_market_making_operations_info_click',
                                        })
                                        break
                                    case 'treasury_operations':
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'treasuries_operations',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_treasury_operations_info_click',
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(id)
                                }
                            }}
                            segments={[
                                {
                                    id: 'lending_operations',
                                    label: formatMessage({
                                        id: 'legend.lending-operations',
                                        defaultMessage: 'Lending Operations',
                                    }),
                                    value: unsafe_toNumberWithFraction(
                                        INCOME_SOURCES_2024.lendingOperations
                                            .amount,
                                        INCOME_SOURCES_2024.lendingOperations
                                            .currency.fraction
                                    ),
                                    displayValue: formattedFiatMoneyShort({
                                        money: INCOME_SOURCES_2024.lendingOperations,
                                    }),
                                    color: 'blue30',
                                    icon: ({ color }) => (
                                        <InfoCircleOutline
                                            size={16}
                                            color={color}
                                        />
                                    ),
                                },
                                {
                                    id: 'market_making_operations',
                                    label: formatMessage({
                                        id: 'legend.market_making-operations',
                                        defaultMessage:
                                            'Market Making Operations',
                                    }),
                                    value: unsafe_toNumberWithFraction(
                                        INCOME_SOURCES_2024
                                            .marketMakingOperations.amount,
                                        INCOME_SOURCES_2024
                                            .marketMakingOperations.currency
                                            .fraction
                                    ),
                                    displayValue: formattedFiatMoneyShort({
                                        money: INCOME_SOURCES_2024.marketMakingOperations,
                                    }),
                                    color: 'purple50',
                                    icon: ({ color }) => (
                                        <InfoCircleOutline
                                            size={16}
                                            color={color}
                                        />
                                    ),
                                },
                                {
                                    id: 'treasury_operations',
                                    label: formatMessage({
                                        id: 'legend.treasury-operations',
                                        defaultMessage: 'Treasury Operations',
                                    }),
                                    value: unsafe_toNumberWithFraction(
                                        INCOME_SOURCES_2024.treasuryOperations
                                            .amount,
                                        INCOME_SOURCES_2024.treasuryOperations
                                            .currency.fraction
                                    ),
                                    displayValue: formattedFiatMoneyShort({
                                        money: INCOME_SOURCES_2024.treasuryOperations,
                                    }),
                                    color: 'green30',
                                    icon: ({ color }) => (
                                        <InfoCircleOutline
                                            size={16}
                                            color={color}
                                        />
                                    ),
                                },
                            ]}
                        />
                        <Column spacing={12}>
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="gray5"
                            >
                                <FormattedMessage
                                    id="earn-taker-investment-details.key-facts"
                                    defaultMessage="Key facts"
                                />
                            </Text>
                            <Column spacing={4}>
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.projected-yield"
                                            defaultMessage="Projected annual yield"
                                        />
                                    }
                                    rightText={getFormattedPercentage(
                                        takerApyMap[taker.type]
                                    )}
                                />
                                <MetaDataListItem
                                    leftText={
                                        <Tertiary
                                            color="on_light_bold"
                                            size="large"
                                            onClick={() => {
                                                postUserEvent({
                                                    type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                                    asset: 'usd',
                                                    tooltip: 'base_currency',
                                                    installationId,
                                                })
                                                onMsg({
                                                    type: 'on_base_currency_info_click',
                                                })
                                            }}
                                        >
                                            {({
                                                textVariant,
                                                textWeight,
                                                color,
                                            }) => (
                                                <Row spacing={4}>
                                                    <Text
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                        color={color}
                                                    >
                                                        <FormattedMessage
                                                            id="earn-taker-investment-details.base_currency.label"
                                                            defaultMessage="Base currency"
                                                        />
                                                    </Text>
                                                    <InfoCircleOutline
                                                        size={16}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </Tertiary>
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.label"
                                            defaultMessage="Digital US Dollar"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.issuer"
                                            defaultMessage="Issuer"
                                        />
                                    }
                                    rightText={
                                        <Tertiary
                                            color="on_light_bold"
                                            size="large"
                                            onClick={() =>
                                                openExternalURL(SKY_URL)
                                            }
                                        >
                                            {({
                                                textVariant,
                                                textWeight,
                                                color,
                                            }) => (
                                                <Row spacing={4}>
                                                    <Text
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                        color={color}
                                                    >
                                                        <FormattedMessage
                                                            id="earn-taker-investment-details.sky"
                                                            defaultMessage="Sky"
                                                        />
                                                    </Text>
                                                    <ExternalLink
                                                        size={16}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </Tertiary>
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.asset-class"
                                            defaultMessage="Asset class"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.fixed-income"
                                            defaultMessage="Fixed income"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.liquidity"
                                            defaultMessage="Liquidity"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.withdraw-anytime"
                                            defaultMessage="Withdraw anytime"
                                        />
                                    }
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.yield"
                                            defaultMessage="Yield"
                                        />
                                    }
                                    rightText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.accrued-realtime"
                                            defaultMessage="Accrued in real-time"
                                        />
                                    }
                                />
                            </Column>
                        </Column>

                        <Column spacing={12}>
                            <Row spacing={0} alignX="stretch" alignY="baseline">
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="gray5"
                                >
                                    <FormattedMessage
                                        id="earn-taker-investment-details.asset-coverage-ratio"
                                        defaultMessage="Asset coverage ratio"
                                    />
                                </Text>
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="gray5"
                                >
                                    {(() => {
                                        switch (takerMetricsLoadable.type) {
                                            case 'loading':
                                            case 'error':
                                                return (
                                                    <Skeleton
                                                        variant="default"
                                                        height={18}
                                                        width={45}
                                                    />
                                                )
                                            case 'loaded':
                                            case 'reloading':
                                            case 'subsequent_failed':
                                                return `${takerMetricsLoadable.data.assetCoverageRatio.toFixed(
                                                    2
                                                )}x`
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(
                                                    takerMetricsLoadable
                                                )
                                        }
                                    })()}
                                </Text>
                            </Row>
                            <Column spacing={4}>
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.depositor-obligations"
                                            defaultMessage="Depositor obligations"
                                        />
                                    }
                                    rightText={(() => {
                                        switch (takerMetricsLoadable.type) {
                                            case 'loading':
                                            case 'error':
                                                return (
                                                    <Skeleton
                                                        variant="default"
                                                        height={18}
                                                        width={45}
                                                    />
                                                )
                                            case 'loaded':
                                            case 'reloading':
                                            case 'subsequent_failed':
                                                return (
                                                    <FormattedMoneyCompact
                                                        money={
                                                            takerMetricsLoadable
                                                                .data
                                                                .depositorObligations
                                                        }
                                                    />
                                                )
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(
                                                    takerMetricsLoadable
                                                )
                                        }
                                    })()}
                                />
                                <MetaDataListItem
                                    leftText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.total-collateral"
                                            defaultMessage="Total Collateral"
                                        />
                                    }
                                    rightText={(() => {
                                        switch (takerMetricsLoadable.type) {
                                            case 'loading':
                                            case 'error':
                                                return (
                                                    <Skeleton
                                                        variant="default"
                                                        height={18}
                                                        width={45}
                                                    />
                                                )
                                            case 'loaded':
                                            case 'reloading':
                                            case 'subsequent_failed':
                                                return (
                                                    <FormattedMoneyCompact
                                                        money={
                                                            takerMetricsLoadable
                                                                .data
                                                                .reserveTotal
                                                        }
                                                    />
                                                )
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(
                                                    takerMetricsLoadable
                                                )
                                        }
                                    })()}
                                />
                            </Column>
                            <Tertiary
                                color="on_light"
                                size="regular"
                                onClick={() =>
                                    openExternalURL(SKY_COLLATERAL_URL)
                                }
                            >
                                {({ color, textVariant, textWeight }) => (
                                    <Row spacing={4}>
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="earn-taker-investment-details.see-realtime"
                                                defaultMessage="See real-time data"
                                            />
                                        </Text>
                                        <ExternalLink size={16} color={color} />
                                    </Row>
                                )}
                            </Tertiary>
                        </Column>
                        <Column spacing={12}>
                            {(() => {
                                switch (takerMetricsLoadable.type) {
                                    case 'loading':
                                    case 'error':
                                        return (
                                            <StackedBarChart.Skeleton
                                                title={
                                                    <FormattedMessage
                                                        id="earn-taker-investment-details.collateral-composition"
                                                        defaultMessage="Collateral composition"
                                                    />
                                                }
                                            />
                                        )
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        const collateral =
                                            takerMetricsLoadable.data
                                                .collateralComposition
                                        const sortedSegments = [
                                            {
                                                id: 'stablecoins' as const,
                                                label: formatMessage({
                                                    id: 'collateral.stablecoins',
                                                    defaultMessage:
                                                        'Stablecoins',
                                                }),
                                                color: 'green30' as const,
                                                value: unsafe_toNumberWithFraction(
                                                    collateral.stableCoins
                                                        .amount,
                                                    collateral.stableCoins
                                                        .currency.fraction
                                                ),
                                                displayValue:
                                                    formattedFiatMoneyShort({
                                                        money: collateral.stableCoins,
                                                    }),
                                            },
                                            {
                                                id: 'bitcoin' as const,
                                                label: formatMessage({
                                                    id: 'collateral.bitcoin',
                                                    defaultMessage: 'Bitcoin',
                                                }),
                                                color: 'orange30' as const,
                                                value: unsafe_toNumberWithFraction(
                                                    collateral.bitcoin.amount,
                                                    collateral.bitcoin.currency
                                                        .fraction
                                                ),
                                                displayValue:
                                                    formattedFiatMoneyShort({
                                                        money: collateral.bitcoin,
                                                    }),
                                            },
                                            {
                                                id: 'ethereum' as const,
                                                label: formatMessage({
                                                    id: 'collateral.ethereum',
                                                    defaultMessage: 'Ethereum',
                                                }),
                                                color: 'blue30' as const,
                                                value: unsafe_toNumberWithFraction(
                                                    collateral.ether.amount,
                                                    collateral.ether.currency
                                                        .fraction
                                                ),
                                                displayValue:
                                                    formattedFiatMoneyShort({
                                                        money: collateral.ether,
                                                    }),
                                            },
                                            {
                                                id: 'real-world-assets' as const,
                                                label: formatMessage({
                                                    id: 'collateral.rwa',
                                                    defaultMessage:
                                                        'Real World Assets',
                                                }),
                                                color: 'purple50' as const,
                                                value: unsafe_toNumberWithFraction(
                                                    collateral.realWorldAssets
                                                        .amount,
                                                    collateral.realWorldAssets
                                                        .currency.fraction
                                                ),
                                                displayValue:
                                                    formattedFiatMoneyShort({
                                                        money: collateral.realWorldAssets,
                                                    }),
                                            },
                                            {
                                                id: 'other-assets' as const,
                                                label: formatMessage({
                                                    id: 'collateral.other-assets',
                                                    defaultMessage:
                                                        'Other Assets',
                                                }),
                                                color: 'blue70' as const,
                                                value: unsafe_toNumberWithFraction(
                                                    collateral.otherAssets
                                                        .amount,
                                                    collateral.otherAssets
                                                        .currency.fraction
                                                ),
                                                displayValue:
                                                    formattedFiatMoneyShort({
                                                        money: collateral.otherAssets,
                                                    }),
                                            },
                                        ].sort((a, b) => b.value - a.value)
                                        return (
                                            <StackedBarChart
                                                title={
                                                    <FormattedMessage
                                                        id="earn-taker-investment-details.collateral-composition"
                                                        defaultMessage="Collateral composition"
                                                    />
                                                }
                                                segments={sortedSegments}
                                            />
                                        )
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(
                                            takerMetricsLoadable
                                        )
                                }
                            })()}
                            <Tertiary
                                color="on_light"
                                size="regular"
                                onClick={() =>
                                    openExternalURL(SKY_COLLATERAL_URL)
                                }
                            >
                                {({ color, textVariant, textWeight }) => (
                                    <Row spacing={4}>
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="earn-taker-investment-details.see-realtime"
                                                defaultMessage="See real-time data"
                                            />
                                        </Text>
                                        <ExternalLink size={16} color={color} />
                                    </Row>
                                )}
                            </Tertiary>
                        </Column>
                        <Column spacing={16}>
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="gray5"
                            >
                                <FormattedMessage
                                    id="earn-taker-investment-details.faq"
                                    defaultMessage="FAQ"
                                />
                            </Text>
                            <Column spacing={8}>
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'faq_how_is_backed',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_faq_how_is_backed_clicked',
                                        })
                                    }}
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.how-is-backed"
                                            defaultMessage="How is Sky USD backed, and what happens to my money if Zeal goes bankrupt?"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <InfoCircleOutline
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'faq_can_lose_principal',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_faq_can_lose_principal_clicked',
                                        })
                                    }}
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.lose-principal"
                                            defaultMessage="Can I realistically lose my principal, and under what circumstances?"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <InfoCircleOutline
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'faq_high_returns',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_faq_high_returns_clicked',
                                        })
                                    }}
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.high-returns"
                                            defaultMessage="How can the returns be so high, especially compared to traditional banks?"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <InfoCircleOutline
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'faq_ftx_difference',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_faq_ftx_difference_clicked',
                                        })
                                    }}
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.ftx-difference"
                                            defaultMessage="How is this different from FTX, Celsius, BlockFi, or Luna?"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <InfoCircleOutline
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() => {
                                        postUserEvent({
                                            type: 'EarnAssetDetailsMoreInfoClickedEvent',
                                            asset: 'usd',
                                            tooltip: 'faq_insurance',
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_faq_insurance_clicked',
                                        })
                                    }}
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.usd.insurance"
                                            defaultMessage="Are my funds insured or guaranteed by any entity (like FDIC or similar)?"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <InfoCircleOutline
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                                <ListItemButton
                                    wrapPrimaryText
                                    variant="outline"
                                    background="surface"
                                    aria-current={false}
                                    onClick={() =>
                                        openExternalURL(SKY_ZEAL_FAQ_URL)
                                    }
                                    disabled={false}
                                    primaryText={
                                        <FormattedMessage
                                            id="earn-taker-investment-details.see-other-faq"
                                            defaultMessage="See all other FAQs"
                                        />
                                    }
                                    side={{
                                        rightIcon: ({ size }) => (
                                            <ExternalLink
                                                size={size}
                                                color="gray40"
                                            />
                                        ),
                                    }}
                                />
                            </Column>
                        </Column>
                    </Column>
                </ScrollContainer>
                {(() => {
                    switch (variant) {
                        case 'just_information':
                            return null
                        case 'information_and_interaction':
                            return (
                                <Actions variant="overlay">
                                    {sumTakerPortfolio({
                                        taker,
                                        takerPortfolioMap,
                                        defaultCurrencyConfig,
                                    }).amount > FIAT_DUST && (
                                        <Button
                                            onClick={() =>
                                                onMsg({
                                                    type: 'on_earn_withdrawal_asset_click',
                                                    taker,
                                                })
                                            }
                                            variant="secondary"
                                            size="regular"
                                        >
                                            <FormattedMessage
                                                id="withdraw"
                                                defaultMessage="Withdraw"
                                            />
                                        </Button>
                                    )}
                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            postUserEvent({
                                                type: 'EarnDepositButtonClickedEvent',
                                                asset: 'usd',
                                                location:
                                                    'investment_details_screen',
                                                installationId,
                                            })
                                            onMsg({
                                                type: 'on_earn_deposit_asset_click',
                                                taker,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="deposit"
                                            defaultMessage="Deposit"
                                        />
                                    </Button>
                                </Actions>
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(variant)
                    }
                })()}
            </Column>
        </Screen>
    )
}

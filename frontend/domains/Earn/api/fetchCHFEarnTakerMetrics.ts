import { externalFetch } from '@zeal/api/externalFetch'

import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { values } from '@zeal/toolkit/Object'
import {
    arrayOf,
    groupByType,
    number,
    Obj,
    object,
    Result,
    shape,
    string,
    UnexpectedResultFailureError,
} from '@zeal/toolkit/Result'

import { CHFEarnTakerMetrics } from '@zeal/domains/Earn'
import { EARN_CHF_TAKER_USER_CURRENCY } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

type CollateralGroups = {
    bitcoin: number
    ethereum: number
    realWorldAssets: number
    other: number
}

type CollateralItem = {
    symbol: string
    amount: number
}

const BITCOIN = new Set(['WBTC', 'cbBTC'])
const ETHEREUM = new Set(['WETH', 'LsETH', 'wstETH'])
const RWA = new Set(['BOSS', 'DQTS', 'REALU'])
const OTHER = new Set(['CRV', 'UNI', 'GNO', 'FPS', 'VCHF'])

const groupCollateralItems = (
    items: CollateralItem[]
): { grouped: CollateralGroups; unknownAssets: string[] } =>
    items.reduce(
        (map, item) => {
            if (BITCOIN.has(item.symbol)) {
                map.grouped.bitcoin += item.amount
                return map
            }

            if (ETHEREUM.has(item.symbol)) {
                map.grouped.ethereum += item.amount
                return map
            }

            if (RWA.has(item.symbol)) {
                map.grouped.realWorldAssets += item.amount
                return map
            }

            if (OTHER.has(item.symbol)) {
                map.grouped.other += item.amount
                return map
            }

            map.grouped.other += item.amount
            map.unknownAssets.push(item.symbol)
            return map
        },
        {
            grouped: {
                bitcoin: 0,
                ethereum: 0,
                realWorldAssets: 0,
                other: 0,
            } as CollateralGroups,
            unknownAssets: [] as string[],
        }
    )

export const fetchCHFEarnTakerMetrics =
    async (): Promise<CHFEarnTakerMetrics> => {
        const [totalSupplyResponse, collateralResponse] = await Promise.all([
            externalFetch(
                'https://api.frankencoin.com/ecosystem/frankencoin/info'
            ).then((r) => r.json()),
            externalFetch(
                'https://api.frankencoin.com/ecosystem/collateral/stats'
            ).then((r) => r.json()),
        ])

        const totalSupplyAmount = parseTotalSupply(
            totalSupplyResponse
        ).getSuccessResultOrThrow('Failed to parse CHF total supply')

        const tokens = parseCollateral(
            collateralResponse
        ).getSuccessResultOrThrow('Failed to parse CHF collateral')

        const [errors, parsedCollateralItems] = groupByType(
            tokens.map(parseCollateralItem)
        )

        if (errors.length) {
            captureError(
                new UnexpectedResultFailureError(
                    'Failed to parse some CHF collateral items',
                    errors
                )
            )
        }

        const collateralGroups = groupCollateralItems(parsedCollateralItems)

        if (collateralGroups.unknownAssets.length) {
            captureError(
                new UnexpectedResultFailureError(
                    'Unknown CHF collateral assets found',
                    collateralGroups.unknownAssets
                )
            )
        }

        const totalCollateral = parsedCollateralItems.reduce(
            (acc, item) => acc + item.amount,
            0
        )

        return {
            totalSupply: {
                amount: fromFixedWithFraction(
                    totalSupplyAmount.toString(10),
                    EARN_CHF_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            totalCollateral: {
                amount: fromFixedWithFraction(
                    totalCollateral.toString(10),
                    EARN_CHF_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            assetCoverageRatio: totalCollateral / totalSupplyAmount,
            collateralComposition: {
                bitcoin: {
                    amount: fromFixedWithFraction(
                        collateralGroups.grouped.bitcoin.toString(10),
                        EARN_CHF_TAKER_USER_CURRENCY.fraction
                    ),
                    currency: EARN_CHF_TAKER_USER_CURRENCY,
                },
                ethereum: {
                    amount: fromFixedWithFraction(
                        collateralGroups.grouped.ethereum.toString(10),
                        EARN_CHF_TAKER_USER_CURRENCY.fraction
                    ),
                    currency: EARN_CHF_TAKER_USER_CURRENCY,
                },
                realWorldAssets: {
                    amount: fromFixedWithFraction(
                        collateralGroups.grouped.realWorldAssets.toString(10),
                        EARN_CHF_TAKER_USER_CURRENCY.fraction
                    ),
                    currency: EARN_CHF_TAKER_USER_CURRENCY,
                },
                other: {
                    amount: fromFixedWithFraction(
                        collateralGroups.grouped.other.toString(10),
                        EARN_CHF_TAKER_USER_CURRENCY.fraction
                    ),
                    currency: EARN_CHF_TAKER_USER_CURRENCY,
                },
            },
        }
    }

const parseTotalSupply = (input: unknown): Result<unknown, number> =>
    object(input).andThen((obj) =>
        object(obj.token).andThen((tokenObj) => number(tokenObj.supply))
    )

const parseCollateral = (input: unknown): Result<unknown, Obj[]> =>
    object(input).andThen((obj) =>
        object(obj.map)
            .map((mapObj) => values(mapObj))
            .andThen((vals) => arrayOf(vals, object))
    )

const parseCollateralItem = (input: unknown): Result<unknown, CollateralItem> =>
    object(input).andThen((obj) =>
        shape({
            symbol: string(obj.symbol),
            amount: object(obj.totalValueLocked).andThen((tvlObj) =>
                number(tvlObj.chf)
            ),
        })
    )

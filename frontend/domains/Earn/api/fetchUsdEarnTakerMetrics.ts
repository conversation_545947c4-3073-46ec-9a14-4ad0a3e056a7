import { externalFetch } from '@zeal/api/externalFetch'
import { get } from '@zeal/api/requestBackend'

import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import {
    arrayOf,
    groupByType,
    number,
    object,
    Result,
    shape,
    string,
    UnexpectedResultFailureError,
    unknown,
} from '@zeal/toolkit/Result'

import { UsdEarnTakerMetrics } from '@zeal/domains/Earn'
import { EARN_USD_TAKER_USER_CURRENCY } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

type SkyCollateralGroups = {
    stableCoins: number
    bitcoin: number
    ether: number
    realWorldAssets: number
    otherAssets: number
}

type SkyCollateralItem = {
    symbol: string
    amount: number
}

const PT_STABLECOIN_PREFIXES = [
    'PT-eUSDE-',
    'PT-sUSDE-',
    'PT-USDS-',
    'PT-USDe-',
    'PT-staking-USDS-',
]

const STABLE_COINS = new Set([
    'DAI',
    'EURC',
    'G-UNI',
    'LP_USR',
    'PSM3',
    'PYUSD',
    'PYUSDUSDS',
    'eUSDe',
    'sDAI',
    'sUSDS',
    'sUSDSUSDT',
    'sUSDe',
    'USDC',
    'USDS',
    'USDT',
    'USDbC',
    'USDe',
    'PT_USDE',
    'PT_sUSDE',
])

const BITCOIN = new Set(['BTC', 'cbBTC', 'LBTC', 'tBTC', 'WBTC', 'eBTC'])

const ETHER = new Set([
    'ETH',
    'WETH',
    'cbETH',
    'ezETH',
    'ETHx',
    'osETH',
    'rETH',
    'rsETH',
    'weETH',
    'wrsETH',
    'wstETH',
])

const REAL_WORLD_ASSETS = new Set(['BUIDL-I', 'JAAA', 'JTRSY', 'USTB', 'RWA'])

const OTHER_ASSETS = new Set([
    '1INCH',
    'AAVE',
    'BAL',
    'CRV',
    'ENS',
    'HYPE',
    'LINK',
    'LDO',
    'MKR',
    'SKY',
    'SNX',
    'SOL',
    'UNI',
    'UNI-V2',
    'XRP',
    'jitoSOL',
])

const SKY_BACKED_ENDPOINT = 'https://open.data.blockanalitica.com/v1/backed/'

export const fetchUsdEarnTakerMetrics = async ({
    signal,
}: {
    signal?: AbortSignal
}): Promise<UsdEarnTakerMetrics> => {
    const [totalSupplyResponse, collateralResponse] = await Promise.all([
        get('/proxy/cba/sky/usds/total-supply', {}, signal),
        externalFetch(SKY_BACKED_ENDPOINT, { signal }).then((response) =>
            response.json()
        ),
    ])

    const totalSupply = number(totalSupplyResponse).getSuccessResultOrThrow(
        'Failed to parse Sky total supply response'
    )

    const collateralItems = object(collateralResponse)
        .andThen((obj) => arrayOf(obj.data, unknown))
        .getSuccessResultOrThrow('Failed to parse Sky collateral response')

    const [errors, parsedCollateralItems] = groupByType(
        collateralItems.map(parseSkyCollateralItem)
    )

    if (errors.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Failed to parse some sky collateral items',
                errors
            )
        )
    }

    const collateralGroups = groupCollateralItems(parsedCollateralItems)

    if (collateralGroups.unknownAssets.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Unknown sky collateral assets found',
                { assets: collateralGroups.unknownAssets }
            )
        )
    }

    const totalCollateral = parsedCollateralItems.reduce(
        (acc, item) => acc + item.amount,
        0
    )

    return {
        reserveTotal: {
            amount: fromFixedWithFraction(
                totalCollateral.toString(10),
                EARN_USD_TAKER_USER_CURRENCY.fraction
            ),
            currency: EARN_USD_TAKER_USER_CURRENCY,
        },
        depositorObligations: {
            amount: fromFixedWithFraction(
                totalSupply.toString(10),
                EARN_USD_TAKER_USER_CURRENCY.fraction
            ),
            currency: EARN_USD_TAKER_USER_CURRENCY,
        },
        assetCoverageRatio: totalCollateral / totalSupply,
        collateralComposition: {
            stableCoins: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.stableCoins.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            bitcoin: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.bitcoin.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            ether: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.ether.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            realWorldAssets: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.realWorldAssets.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            otherAssets: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.otherAssets.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
    }
}

const groupCollateralItems = (
    items: SkyCollateralItem[]
): { grouped: SkyCollateralGroups; unknownAssets: string[] } =>
    items.reduce(
        (map, item) => {
            if (
                STABLE_COINS.has(item.symbol) ||
                PT_STABLECOIN_PREFIXES.some((prefix) =>
                    item.symbol.startsWith(prefix)
                )
            ) {
                map.grouped.stableCoins += item.amount
                return map
            }

            if (BITCOIN.has(item.symbol)) {
                map.grouped.bitcoin += item.amount
                return map
            }

            if (ETHER.has(item.symbol)) {
                map.grouped.ether += item.amount
                return map
            }

            if (REAL_WORLD_ASSETS.has(item.symbol)) {
                map.grouped.realWorldAssets += item.amount
                return map
            }

            if (OTHER_ASSETS.has(item.symbol)) {
                map.grouped.otherAssets += item.amount
                return map
            }

            map.grouped.otherAssets += item.amount
            map.unknownAssets.push(item.symbol)
            return map
        },
        {
            grouped: {
                stableCoins: 0,
                bitcoin: 0,
                ether: 0,
                realWorldAssets: 0,
                otherAssets: 0,
            } as SkyCollateralGroups,
            unknownAssets: [] as string[],
        }
    )

const parseSkyCollateralItem = (
    input: unknown
): Result<unknown, SkyCollateralItem> =>
    object(input).andThen((obj) =>
        shape({
            symbol: string(obj.collateral_symbol),
            amount: number(obj.backed_total),
        })
    )

import { FormattedMessage } from 'react-intl'
import { SectionListData } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Rocket } from '@zeal/uikit/Icon/Rocket'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { SectionList } from '@zeal/uikit/SectionList'
import { Text } from '@zeal/uikit/Text'

import { CryptoCurrency, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { Network, NetworkMap, PredefinedNetwork } from '@zeal/domains/Network'
import { Avatar as NetworkAvatar } from '@zeal/domains/Network/components/Avatar'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'
import {
    NonSponsoredGasAbstractionTransactionFee,
    NonSponsoredUserOperationFee,
} from '@zeal/domains/UserOperation'

type Props = {
    networkMap: NetworkMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    fee: NonSponsoredUserOperationFee
    portfolio: ServerPortfolio2
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_4337_auto_gas_token_selection_clicked'; network: Network }
    | {
          type: 'on_4337_gas_currency_selected'
          selectedGasCurrency: CryptoCurrency
      }

export const GasTokenSelector = ({
    fee,
    portfolio,
    gasCurrencyPresetMap,
    networkMap,
    installationId,
    onMsg,
}: Props) => {
    const network = networkMap[
        fee.selectedFee.feeInTokenCurrency.currency.networkHexChainId
    ] as PredefinedNetwork

    const autoGasTokenSelectionEnabled =
        !gasCurrencyPresetMap[network.hexChainId]

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12}>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="GasCurrencySelector.networkFee"
                                        defaultMessage="Network fee"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <NetworkAvatar
                            currentNetwork={{
                                type: 'specific_network',
                                network,
                            }}
                            size={24}
                        />
                    }
                />

                <Column spacing={0}>
                    <Group variant="default">
                        <ListItem
                            variant="default"
                            size="regular"
                            onClick={() => {
                                onMsg({
                                    type: 'on_4337_auto_gas_token_selection_clicked',
                                    network,
                                })
                            }}
                            aria-current={autoGasTokenSelectionEnabled}
                            avatar={({ size }) => (
                                <Rocket size={size} color="iconAccent2" />
                            )}
                            primaryText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.title"
                                    defaultMessage="Automatic fee handling"
                                />
                            }
                            shortText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.description"
                                    defaultMessage="Pay fees from the largest balance"
                                />
                            }
                        />
                    </Group>
                </Column>
                <Content
                    installationId={installationId}
                    fee={fee}
                    portfolio={portfolio}
                    onMsg={onMsg}
                    autoGasTokenSelectionEnabled={autoGasTokenSelectionEnabled}
                />
            </Column>
        </Screen>
    )
}

const Content = ({
    fee,
    portfolio,
    autoGasTokenSelectionEnabled,
    installationId,
    onMsg,
}: {
    fee: NonSponsoredUserOperationFee
    portfolio: ServerPortfolio2
    autoGasTokenSelectionEnabled: boolean
    installationId: string
    onMsg: (msg: Msg) => void
}) => {
    const portfolioFeeOptions: NonSponsoredGasAbstractionTransactionFee[] =
        fee.feeOptions.filter(({ feeInTokenCurrency }) =>
            portfolio.tokens
                .map((t) => t.balance.currency.id)
                .includes(feeInTokenCurrency.currency.id)
        )

    const nonPortfolioFeeOptions: NonSponsoredGasAbstractionTransactionFee[] =
        fee.feeOptions.filter(
            ({ feeInTokenCurrency }) =>
                !portfolioFeeOptions
                    .map((o) => o.feeInTokenCurrency.currency.id)
                    .includes(feeInTokenCurrency.currency.id)
        )

    const sections: SectionListData<NonSponsoredGasAbstractionTransactionFee>[] =
        [
            {
                data: portfolioFeeOptions,
            },
            {
                data: nonPortfolioFeeOptions,
            },
        ]

    return (
        <Column spacing={12} fill shrink>
            <Column spacing={4} shrink>
                <Row spacing={0}>
                    <Text
                        variant="paragraph"
                        color="textSecondary"
                        weight="regular"
                    >
                        <FormattedMessage
                            id="GasCurrencySelector.payNetworkFeesUsing"
                            defaultMessage="Pay network fees using"
                        />
                    </Text>
                </Row>

                <SectionList
                    variant="grouped"
                    itemSpacing={8}
                    sectionSpacing={8}
                    sections={sections}
                    renderItem={({ item }) => {
                        const { currency } = item.feeInTokenCurrency

                        return (
                            <FeeOptionListItem
                                portfolio={portfolio}
                                key={currency.id}
                                item={item}
                                aria-current={
                                    autoGasTokenSelectionEnabled
                                        ? false
                                        : fee.selectedFee.feeInTokenCurrency
                                              .currency.id === currency.id
                                }
                                onClick={() => {
                                    postUserEvent({
                                        type: 'GasCurrencySelectedEvent',
                                        installationId,
                                        networkHexId:
                                            currency.networkHexChainId,
                                        currencyId: currency.id,
                                    })
                                    return onMsg({
                                        type: 'on_4337_gas_currency_selected',
                                        selectedGasCurrency: currency,
                                    })
                                }}
                            />
                        )
                    }}
                />
            </Column>
        </Column>
    )
}

const FeeOptionListItem = ({
    item,
    portfolio,
    'aria-current': ariaCurrent,
    onClick,
}: {
    'aria-current': boolean
    item: NonSponsoredGasAbstractionTransactionFee
    portfolio: ServerPortfolio2 | null
    onClick: () => void
}) => {
    const { currency } = item.feeInTokenCurrency

    const balance = portfolio
        ? getBalanceByCryptoCurrency2({
              currency,
              serverPortfolio: portfolio,
          })
        : {
              amount: 0n,
              currency,
          }

    return (
        <ListItem
            size="large"
            aria-current={ariaCurrent}
            primaryText={currency.code}
            onClick={onClick}
            avatar={({ size }) => (
                <CurrencyAvatar
                    rightBadge={() => null}
                    size={size}
                    currency={currency}
                />
            )}
            shortText={
                <Text>
                    <FormattedMessage
                        id="GasCurrencySelector.balance"
                        defaultMessage="Balance: {balance}"
                        values={{
                            balance: (
                                <FormattedMoneyPrecise
                                    withSymbol={false}
                                    sign={null}
                                    money={balance}
                                />
                            ),
                        }}
                    />
                </Text>
            }
            side={{
                title: item.feeInDefaultCurrency ? (
                    <FormattedMoneyCompact money={item.feeInDefaultCurrency} />
                ) : (
                    ' ' // non-breaking-space thing to keep right side at right place
                ),
                subtitle: item.feeInTokenCurrency ? (
                    <FormattedMoneyPrecise
                        withSymbol={false}
                        sign={null}
                        money={item.feeInTokenCurrency}
                    />
                ) : (
                    ' ' // non-breaking-space thing to keep right side at right place
                ),
            }}
        />
    )
}

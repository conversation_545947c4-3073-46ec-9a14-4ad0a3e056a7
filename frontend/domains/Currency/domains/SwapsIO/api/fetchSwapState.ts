import { get } from '@zeal/api/swapsIOApi'

import { withRetries } from '@zeal/toolkit/Function'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'

import { CryptoMoney } from '@zeal/domains/Money'

import { SwapsIOSwapRequest } from '..'
import { parseSwapRequest } from '../parsers/parseSwapRequest'
import { parseSwapsIOSwapRequest } from '../parsers/parseSwapsIOSwapRequest'

const NUM_RETRIES = 2
const RETRY_DELAY_MS = 2000

const fetchSwapState = async ({
    hash,
    from,
    to,
    signal,
}: {
    hash: Hexadecimal
    from: CryptoMoney
    to: CryptoMoney
    signal?: AbortSignal
}): Promise<SwapsIOSwapRequest> => {
    const response = await get(`/swaps/${hash}`, {}, signal)
    const rawRequest = parseSwapRequest(response).getSuccessResultOrThrow(
        'Failed to parse raw swaps.io swap request'
    )

    return parseSwapsIOSwapRequest({
        rawSwapRequest: rawRequest,
        knownCurrencies: {
            [from.currency.id]: from.currency,
            [to.currency.id]: to.currency,
        },
    }).getSuccessResultOrThrow(`Failed to parse swaps.io swap request`)
}

export const fetchSwapStateWithRetries = withRetries({
    retries: NUM_RETRIES,
    delayMs: RETRY_DELAY_MS,
    fn: fetchSwapState,
})

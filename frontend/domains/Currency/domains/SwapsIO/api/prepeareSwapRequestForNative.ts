import { ImperativeError } from '@zeal/toolkit/Error'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

import { createSwapWithRetries } from './createSwap'
import { manualReceiveWithRetries } from './manualReceive'
import { submitSwapWithoutSignature } from './submitSwap'

import { SwapsIOSwapRequest } from '..'
import { SWAPS_IO_DIAMOND_CONTRACTS } from '../constants'

export const prepeareSwapRequestForNative = async ({
    signal,
    sender,
    fromAmount,
    receiver,
    toAmount,
    networkMap,
}: {
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    signal?: AbortSignal
    sender: Web3.address.Address
    receiver: Web3.address.Address
    networkMap: NetworkMap
}): Promise<{
    request: SwapsIOSwapRequest
    transaction: EthSendTransaction
}> => {
    const request = await createSwapWithRetries({
        from: fromAmount,
        to: toAmount,
        sender,
        receiver,
        networkMap,
        signal,
    })

    await submitSwapWithoutSignature({
        request,
        signal,
    })

    const data = await manualReceiveWithRetries({
        request,
        signal,
    })

    if (
        !SWAPS_IO_DIAMOND_CONTRACTS.has(data.toAddress) ||
        data.fromAddress !== request.sender ||
        data.value !== request.from.amount
    ) {
        throw new ImperativeError(`Swaps.io native swap data mismatched`, {
            data: data,
            request: request,
        })
    }

    const transaction: EthSendTransaction = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_sendTransaction',
        params: [
            {
                from: data.fromAddress,
                to: data.toAddress,
                data: data.data,
                value: fromBigInt(data.value),
            },
        ],
    }
    return {
        transaction,
        request,
    }
}

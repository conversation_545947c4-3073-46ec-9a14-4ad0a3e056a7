import * as Web3 from '@zeal/toolkit/Web3'

import { SigningKeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { createSwapWithRetries } from './createSwap'
import { fetchSwapMsgToSignWithRetries } from './fetchSwapMsgToSign'
import { submitSwap } from './submitSwap'

import { SwapsIOSwapRequest } from '..'

export const prepareAndSubmitSwap = async ({
    networkMap,
    sender,
    receiver,
    from,
    sessionPassword,
    to,
    signal,
    keyStore,
}: {
    from: CryptoMoney
    to: CryptoMoney
    sender: Web3.address.Address
    receiver: Web3.address.Address | null
    sessionPassword: string
    keyStore: SigningKeyStore

    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<SwapsIOSwapRequest> => {
    const network = findNetworkByHexChainId(
        from.currency.networkHexChainId,
        networkMap
    )
    const request = await createSwapWithRetries({
        from,
        to,
        sender,
        receiver,
        networkMap,
        signal,
    })
    const msgToSign = await fetchSwapMsgToSignWithRetries({
        request,
        networkMap,
        signal,
    })

    const signature = await signMessage({
        sessionPassword,
        request: msgToSign,
        keyStore,
        network,
        dApp: null,
    })

    return await submitSwap({ request, signature })
}
